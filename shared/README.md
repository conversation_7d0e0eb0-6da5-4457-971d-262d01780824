# PromptMate 共享存储模块

## 概述

本模块提供了统一的数据存储和管理功能，用于替代之前在各个文件中重复的数据库操作代码。通过抽取通用的存储操作，提高了代码的可维护性和一致性。

## 文件结构

```
shared/
├── storage.js          # 主存储模块
├── config.js           # 配置常量文件
└── README.md          # 本文档
```

## 主要功能

### 配置管理 (config.js)

统一管理应用的配置常量，避免硬编码：

```javascript
import { STORAGE_CONFIG, APP_CONFIG } from '../shared/config.js';

// 存储配置
console.log(STORAGE_CONFIG.STORAGE_KEY); // 'PromptMateDatabase'
console.log(STORAGE_CONFIG.MAX_PROMPTS); // 1000

// 应用配置
console.log(APP_CONFIG.APP_NAME); // 'PromptMate'
console.log(APP_CONFIG.VERSION); // '1.0.0'
```

### PromptStorage 类

`PromptStorage` 类提供了完整的提示词数据管理功能：

#### 基础操作
- `loadPrompts()` - 加载所有提示词
- `savePrompts(promptsArray)` - 保存提示词数组
- `addPrompt(prompt)` - 添加新提示词
- `updatePrompt(id, updates)` - 更新提示词
- `deletePrompt(id)` - 删除提示词
- `deletePrompts(ids)` - 批量删除提示词

#### 查询操作
- `findPromptById(id)` - 根据ID查找提示词
- `searchPrompts(searchTerm)` - 搜索提示词
- `sortPrompts(prompts, order)` - 排序提示词
- `getAllTags()` - 获取所有标签

#### 数据管理
- `importPrompts(importData, merge)` - 导入提示词数据
- `exportPrompts()` - 导出提示词数据
- `clearAll()` - 清空所有数据
- `getStats()` - 获取存储统计信息

#### 事件监听
- `addListener(callback)` - 添加存储变化监听器
- `removeListener(callback)` - 移除存储变化监听器

## 使用方法

### 导入模块

```javascript
// ES6 模块导入
const { promptStorage } = await import('../shared/storage.js');

// 或者使用类
const { PromptStorage } = await import('../shared/storage.js');
const storage = new PromptStorage();
```

### 基本操作示例

```javascript
// 加载提示词
const prompts = await promptStorage.loadPrompts();

// 添加新提示词
const newPrompts = await promptStorage.addPrompt({
    title: '示例提示词',
    promptContent: '这是提示词内容',
    tags: ['标签1', '标签2'],
    notes: '备注信息'
});

// 更新提示词
const updatedPrompts = await promptStorage.updatePrompt('prompt-id', {
    title: '更新后的标题'
});

// 删除提示词
const remainingPrompts = await promptStorage.deletePrompt('prompt-id');

// 搜索提示词
const searchResults = await promptStorage.searchPrompts('关键词');
```

### 监听存储变化

```javascript
// 添加监听器
const handleStorageChange = (type, data) => {
    console.log('存储发生变化:', type, data);
};

promptStorage.addListener(handleStorageChange);

// 移除监听器
promptStorage.removeListener(handleStorageChange);
```

## 数据结构

### 提示词对象结构

```javascript
{
    id: string,              // 唯一标识符
    title: string,           // 标题
    summary: string,         // 摘要（可选）
    promptContent: string,   // 提示词内容
    tags: string[],          // 标签数组
    author: string,          // 作者（可选）
    version: string,         // 版本（可选）
    notes: string,           // 备注（可选）
    time: number            // 时间戳
}
```



## 重构内容

### 已重构的文件

1. **management_page/management.js**
   - 移除重复的 `loadPromptsFromStorage` 和 `savePromptsToStorage` 函数
   - 使用共享存储模块的方法
   - 保持原有功能不变

2. **sidebar/sidebar.js**
   - 移除重复的存储操作代码
   - 使用共享存储模块
   - 保留标签更新逻辑

3. **popup/popup.js**
   - 将 mock 数据操作替换为真实存储操作
   - 使用共享存储模块进行 CRUD 操作
   - 改进错误处理

### 重构优势

1. **代码复用** - 消除了重复的存储操作代码
2. **统一接口** - 所有文件使用相同的存储接口
3. **更好的错误处理** - 统一的错误处理机制
4. **功能增强** - 提供更多实用的数据操作方法
5. **易于维护** - 存储逻辑集中管理
6. **向后兼容** - 自动处理旧数据格式

## 注意事项

1. **异步操作** - 所有存储操作都是异步的，需要使用 `await` 或 `.then()`
2. **错误处理** - 建议在调用存储方法时使用 try-catch 进行错误处理
3. **数据验证** - 模块会自动验证数据格式，无效数据会被过滤
4. **性能考虑** - 大量数据操作时建议使用批量方法

## 扩展性

模块设计为可扩展的，可以轻松添加新的存储方法或修改现有逻辑，而不影响使用该模块的其他文件。