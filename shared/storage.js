/**
 * PromptMate 数据存储模块
 * 提供统一的数据存储和管理功能
 */

import { STORAGE_CONFIG } from './config.js';

class PromptStorage {
    constructor() {
        this.STORAGE_KEY = STORAGE_CONFIG.STORAGE_KEY;
        this.listeners = new Set();
        this.isExtensionEnvironment = typeof chrome !== 'undefined' && chrome.storage;
        this.setupStorageListener();
    }

    /**
     * 从存储中加载所有提示词
     * @returns {Promise<Array>} 提示词数组
     */
    async loadPrompts() {
        try {
            let prompts = [];
            if (this.isExtensionEnvironment) {
                const result = await chrome.storage.local.get([this.STORAGE_KEY]);
                prompts = result[this.STORAGE_KEY] || [];
            } else {
                // 使用localStorage作为后备方案
                const stored = localStorage.getItem(this.STORAGE_KEY);
                prompts = stored ? JSON.parse(stored) : [];
            }
            console.log('Prompts loaded from storage:', prompts.length);
            return prompts;
        } catch (error) {
            console.error('Error loading prompts from storage:', error);
            return []; // 返回空数组而不是抛出错误
        }
    }

    /**
     * 保存提示词数组到存储
     * @param {Array} promptsArray 提示词数组
     * @returns {Promise<void>}
     */
    async savePrompts(promptsArray) {
        try {
            if (this.isExtensionEnvironment) {
                await chrome.storage.local.set({ [this.STORAGE_KEY]: promptsArray });
            } else {
                // 使用localStorage作为后备方案
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(promptsArray));
            }
            console.log('Prompts saved to storage:', promptsArray.length);
            this.notifyListeners('save', promptsArray);
        } catch (error) {
            console.error('Error saving prompts to storage:', error);
            throw new Error('保存提示词失败！');
        }
    }

    /**
     * 添加新提示词
     * @param {Object} prompt 提示词对象
     * @returns {Promise<Array>} 更新后的提示词数组
     */
    async addPrompt(prompt) {
        const prompts = await this.loadPrompts();
        const newPrompt = {
            id: crypto.randomUUID(),
            time: Date.now(),
            ...prompt
        };
        prompts.unshift(newPrompt);
        await this.savePrompts(prompts);
        return prompts;
    }

    /**
     * 更新提示词
     * @param {string} id 提示词ID
     * @param {Object} updates 更新的字段
     * @returns {Promise<Array>} 更新后的提示词数组
     */
    async updatePrompt(id, updates) {
        const prompts = await this.loadPrompts();
        const index = prompts.findIndex(p => p.id === id);
        if (index === -1) {
            throw new Error('提示词不存在');
        }
        prompts[index] = {
            ...prompts[index],
            ...updates,
            time: Date.now()
        };
        await this.savePrompts(prompts);
        return prompts;
    }

    /**
     * 删除提示词
     * @param {string} id 提示词ID
     * @returns {Promise<Array>} 更新后的提示词数组
     */
    async deletePrompt(id) {
        const prompts = await this.loadPrompts();
        const filteredPrompts = prompts.filter(p => p.id !== id);
        if (filteredPrompts.length === prompts.length) {
            throw new Error('提示词不存在');
        }
        await this.savePrompts(filteredPrompts);
        return filteredPrompts;
    }

    /**
     * 批量删除提示词
     * @param {Array<string>} ids 提示词ID数组
     * @returns {Promise<Array>} 更新后的提示词数组
     */
    async deletePrompts(ids) {
        const prompts = await this.loadPrompts();
        const filteredPrompts = prompts.filter(p => !ids.includes(p.id));
        await this.savePrompts(filteredPrompts);
        return filteredPrompts;
    }

    /**
     * 根据ID查找提示词
     * @param {string} id 提示词ID
     * @returns {Promise<Object|null>} 提示词对象或null
     */
    async findPromptById(id) {
        const prompts = await this.loadPrompts();
        return prompts.find(p => p.id === id) || null;
    }

    /**
     * 搜索提示词
     * @param {string} searchTerm 搜索词
     * @returns {Promise<Array>} 匹配的提示词数组
     */
    async searchPrompts(searchTerm) {
        const prompts = await this.loadPrompts();
        if (!searchTerm) return prompts;
        
        const term = searchTerm.toLowerCase();
        return prompts.filter(prompt => {
            const contentToSearch = prompt.promptContent || '';
            return (
                (prompt.title && prompt.title.toLowerCase().includes(term)) ||
                contentToSearch.toLowerCase().includes(term) ||
                (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(term))) ||
                (prompt.summary && prompt.summary.toLowerCase().includes(term))
            );
        });
    }

    /**
     * 排序提示词
     * @param {Array} prompts 提示词数组
     * @param {string} order 排序方式: 'date_desc', 'date_asc', 'title_asc', 'title_desc'
     * @returns {Array} 排序后的提示词数组
     */
    sortPrompts(prompts, order = 'date_desc') {
        return [...prompts].sort((a, b) => {
            const titleA = a.title ? a.title.toLowerCase() : '';
            const titleB = b.title ? b.title.toLowerCase() : '';
            const timeA = a.time || 0;
            const timeB = b.time || 0;
            
            switch (order) {
                case 'date_asc':
                    return timeA - timeB;
                case 'title_asc':
                    return titleA.localeCompare(titleB);
                case 'title_desc':
                    return titleB.localeCompare(titleA);
                case 'date_desc':
                default:
                    return timeB - timeA;
            }
        });
    }

    /**
     * 获取所有标签
     * @returns {Promise<Array>} 所有唯一标签的数组
     */
    async getAllTags() {
        const prompts = await this.loadPrompts();
        const tags = new Set();
        prompts.forEach(prompt => {
            if (prompt.tags && prompt.tags.length > 0) {
                prompt.tags.forEach(tag => tags.add(tag));
            }
        });
        return Array.from(tags);
    }

    /**
     * 导入提示词数据
     * @param {Array} importData 导入的数据数组
     * @param {boolean} merge 是否合并现有数据
     * @returns {Promise<Array>} 更新后的提示词数组
     */
    async importPrompts(importData, merge = true) {
        if (!Array.isArray(importData)) {
            throw new Error('导入数据格式无效');
        }

        const validPrompts = importData.filter(p => p.id && p.title && (p.promptContent));
        if (validPrompts.length === 0) {
            throw new Error('没有有效的提示词数据');
        }

        // 数据结构兼容性处理
        const normalizedPrompts = validPrompts.map(prompt => ({
            id: prompt.id || crypto.randomUUID(),
            title: prompt.title,
            summary: prompt.summary || '',
            promptContent: prompt.promptContent || '',
            tags: prompt.tags || [],
            author: prompt.author || '',
            version: prompt.version || '',
            notes: prompt.notes || '',
            time: prompt.time || Date.now()
        }));

        let finalPrompts;
        if (merge) {
            const existingPrompts = await this.loadPrompts();
            const existingIds = new Set(existingPrompts.map(p => p.id));
            const newPrompts = normalizedPrompts.filter(p => !existingIds.has(p.id));
            finalPrompts = [...newPrompts, ...existingPrompts];
        } else {
            finalPrompts = normalizedPrompts;
        }

        await this.savePrompts(finalPrompts);
        return finalPrompts;
    }

    /**
     * 导出提示词数据
     * @returns {Promise<string>} JSON格式的数据字符串
     */
    async exportPrompts() {
        const prompts = await this.loadPrompts();
        return JSON.stringify(prompts, null, 2);
    }

    /**
     * 添加存储变化监听器
     * @param {Function} callback 回调函数
     */
    addListener(callback) {
        this.listeners.add(callback);
    }

    /**
     * 移除存储变化监听器
     * @param {Function} callback 回调函数
     */
    removeListener(callback) {
        this.listeners.delete(callback);
    }

    /**
     * 通知所有监听器
     * @param {string} type 事件类型
     * @param {*} data 事件数据
     */
    notifyListeners(type, data) {
        this.listeners.forEach(callback => {
            try {
                callback(type, data);
            } catch (error) {
                console.error('Error in storage listener:', error);
            }
        });
    }

    /**
     * 设置存储变化监听器
     */
    setupStorageListener() {
        if (this.isExtensionEnvironment && chrome.storage.onChanged) {
            chrome.storage.onChanged.addListener((changes, namespace) => {
                if (namespace === 'local' && changes[this.STORAGE_KEY]) {
                    this.notifyListeners('change', changes[this.STORAGE_KEY].newValue);
                }
            });
        }
        // 在非扩展环境中，localStorage没有内置的变化监听，可以通过自定义事件实现
    }

    /**
     * 清空所有数据
     * @returns {Promise<void>}
     */
    async clearAll() {
        await this.savePrompts([]);
    }

    /**
     * 获取存储统计信息
     * @returns {Promise<Object>} 统计信息对象
     */
    async getStats() {
        const prompts = await this.loadPrompts();
        const tags = await this.getAllTags();
        
        return {
            totalPrompts: prompts.length,
            totalTags: tags.length,
            lastModified: prompts.length > 0 ? Math.max(...prompts.map(p => p.time || 0)) : null
        };
    }
}


// 创建并导出单例实例
export const promptStorage = new PromptStorage();