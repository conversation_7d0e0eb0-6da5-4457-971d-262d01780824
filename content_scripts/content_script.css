#promptmate-fab {
    position: fixed;
    /* --- MODIFIED PROPERTIES --- */
    top: 30%; /* 从顶部向下30%的位置，实现“中间偏上” */
    right: 30px; /* 保持在距离右侧30px的位置，实现“紧靠右边” */
    /* --- END OF MODIFIED PROPERTIES --- */
    width: 56px;
    height: 56px;
    background-color: #FF6F10; /* Primary color */
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px; /* Adjust as needed for 'AI' or icon */
    font-weight: bold;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    z-index: 9999; /* Ensure it's on top */
    transition: transform 0.2s ease-in-out, background-color 0.2s ease;
    user-select: none; /* Prevent text selection */
    
    background-size: 80%; 
    background-repeat: no-repeat;
    background-position: center;
    
}

#promptmate-fab:hover {
    background-color: #FF6F10; /* Darker on hover */
    transform: scale(1.1); /* Adjusted hover effect */
}

#promptmate-fab:active {
    transform: scale(0.95); /* Adjusted active effect */
}