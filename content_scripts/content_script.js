(function() {
    // Create a unique ID for the FAB to avoid conflicts
    const FAB_ID = 'promptmate-fab';

    // Check if FAB already exists
    if (document.getElementById(FAB_ID)) {
        return;
    }

    const fab = document.createElement('div');
    fab.id = FAB_ID;
    fab.title = '打开 AI提示词助手';
    fab.style.backgroundImage = `url(${chrome.runtime.getURL('icons/PromptMate.png')})`;

    // Append to body
    document.body.appendChild(fab);

    let isDragging = false;
    let dragMoved = false;
    let offsetX = 0;
    let offsetY = 0;

    function startDrag(evt) {
        isDragging = true;
        dragMoved = false;
        const e = evt.touches ? evt.touches[0] : evt;
        const rect = fab.getBoundingClientRect();
        offsetX = e.clientX - rect.left;
        offsetY = e.clientY - rect.top;
        document.addEventListener('mousemove', onDrag);
        document.addEventListener('touchmove', onDrag, { passive: false });
        document.addEventListener('mouseup', endDrag);
        document.addEventListener('touchend', endDrag);
    }

    function onDrag(evt) {
        if (!isDragging) return;
        const e = evt.touches ? evt.touches[0] : evt;
        if (evt.touches) evt.preventDefault();
        dragMoved = true;
        fab.style.left = (e.clientX - offsetX) + 'px';
        fab.style.top = (e.clientY - offsetY) + 'px';
        fab.style.right = 'auto';
        fab.style.bottom = 'auto';
    }

    function endDrag() {
        isDragging = false;
        document.removeEventListener('mousemove', onDrag);
        document.removeEventListener('touchmove', onDrag);
        document.removeEventListener('mouseup', endDrag);
        document.removeEventListener('touchend', endDrag);
    }

    fab.addEventListener('mousedown', startDrag);
    fab.addEventListener('touchstart', startDrag);

    // Add click listener
    fab.addEventListener('click', () => {
        if (dragMoved) return;
        console.log('Floating Action Button clicked');
        // Send a message to the background script to open the side panel
        chrome.runtime.sendMessage({ action: "openSidePanel" }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("Error sending message to open side panel:", chrome.runtime.lastError.message);
            } else if (response && response.success) {
                console.log("Side panel open request sent successfully.");
            } else {
                const errorMsg = response && response.error ? response.error : response;
                console.warn("Failed to open side panel:", errorMsg);
            }
        });
    });

})();