const CONTEXT_MENU_ID_COLLECT = "promptPalCollect";
const CONTEXT_MENU_ID_COLLECT_AND_MANAGE = "promptPalCollectAndManage";
const PENDING_PROMPT_SESSION_KEY_BG = 'pendingPromptFromContextMenu';

function getSelectedHtmlFromPage() {
    var sel = window.getSelection();
    if (sel.rangeCount) {
        var container = document.createElement("div");
        for (var i = 0, len = sel.rangeCount; i < len; ++i) {
            container.appendChild(sel.getRangeAt(i).cloneContents());
        }
        return container.innerHTML;
    }
    return '';
}

chrome.runtime.onInstalled.addListener(() => {
    console.log("AI提示词助手插件已安装。");
    chrome.sidePanel.setOptions({ path: 'sidebar/sidebar.html', enabled: true });

    chrome.contextMenus.create({
        id: CONTEXT_MENU_ID_COLLECT,
        title: "使用 PromptMate 收藏",
        contexts: ["selection"]
    });

    chrome.contextMenus.create({
        id: CONTEXT_MENU_ID_COLLECT_AND_MANAGE,
        title: "添加到 PromptMate 并打开管理页面",
        contexts: ["selection"]
    });
});

chrome.action.onClicked.addListener((tab) => {
    chrome.sidePanel.open({ windowId: tab.windowId });
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === "openSidePanel") {
        if (sender.tab && sender.tab.windowId) {
            chrome.sidePanel.open({ windowId: sender.tab.windowId })
                .then(() => sendResponse({ success: true }))
                .catch(error => sendResponse({ success: false, error: error.message }));
            return true;
        }
    }
});


// --- NEW, CORRECTED CONTEXT MENU HANDLER ---
chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (!tab || !tab.id) return;

    // STEP 1: Immediately open the side panel in response to the user gesture.
    chrome.sidePanel.open({ tabId: tab.id }).catch(err => console.error(err));

    // STEP 2: Start fetching the data from the page.
    const actionType = info.menuItemId === CONTEXT_MENU_ID_COLLECT_AND_MANAGE ?
        'collectAndOpenManagement' :
        'collectToSidebar';

    chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: getSelectedHtmlFromPage,
    }, (injectionResults) => {
        // This callback runs after data is fetched. The user gesture context is lost here.
        const selectedHtml = (injectionResults && injectionResults[0] && injectionResults[0].result) || '';
        const isHtml = !!selectedHtml;

        const dataToProcess = {
            selectionHtml: isHtml ? selectedHtml : (info.selectionText || ""),
            actionType: actionType,
            isHtml: isHtml
        };

        // STEP 3: Attempt to send the data directly.
        // We wait a very short time to give the panel a chance to open.
        setTimeout(() => {
            chrome.runtime.sendMessage({
                type: 'NEW_PROMPT_FROM_CONTEXT_MENU',
                data: dataToProcess
            }, (response) => {
                if (chrome.runtime.lastError) {
                    // STEP 4 (FALLBACK): If sending fails, save to session storage.
                    console.warn("Direct message failed, saving to session as fallback:", chrome.runtime.lastError.message);
                    chrome.storage.session.set({ [PENDING_PROMPT_SESSION_KEY_BG]: dataToProcess });
                } else {
                    console.log("Direct message to sidebar successful.");
                }
            });
        }, 150); // 150ms delay
    });
});