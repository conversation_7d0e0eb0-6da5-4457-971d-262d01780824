:root {
    --primary-color: #FF6F10;
    --background: #f8f9fa;
    --surface: #ffffff;
    --surface-alt: #f8f9fa;
    --text-color: #333333;
    --muted-color: #495057;
    --border-color: #dee2e6;
}

.theme-dark {
    --primary-color: #FF6F10;
    --background: #1e1e1e;
    --surface: #2e2e2e;
    --surface-alt: #252525;
    --text-color: #f0f0f0;
    --muted-color: #cccccc;
    --border-color: #555555;
    color-scheme: dark;
}

body {
    font-family: 'Inter', sans-serif;
    margin: 0;
    background-color: var(--background);
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 25px 30px; /* Increased padding */
    background-color: var(--surface);
    border-radius: 10px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    margin-bottom: 25px; /* Increased margin */
    background-color: var(--surface);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.page-header h1 {
    display: flex;
    align-items: center;
    gap: 8px; /* Add spacing between text and icon */
    font-size: 26px; /* Slightly larger */
    font-weight: 600;
    color: inherit;
    margin: 0;
}
.header-actions {
    display: flex;
    gap: 10px;
}

.actions-bar {
    display: flex;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
}

.import-export-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-input-page {
    flex-grow: 1;
    min-width: 250px; /* Minimum width before wrapping */
    max-width: 450px; 
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.search-input-page::placeholder { color: var(--muted-color); }
.search-input-page:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
}
        
.filter-controls select {
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background-color: var(--surface);
    color: var(--text-color); /* <-- 添加此行 */
    height: 40px; /* Match input height */
}

.export-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.export-format-select {
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background-color: var(--surface);
    color: var(--text-color);
    height: 40px;
    min-width: 120px;
    cursor: pointer;
}

.filter-controls select option {
    background-color: var(--surface);
    color: var(--text-color);
}

/* Common Button Styles (copied from sidebar.css for consistency, can be shared) */
.btn {
    padding: 10px 16px; border: none; border-radius: 8px; cursor: pointer;
    font-size: 14px; font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease, color 0.2s ease;
    display: inline-flex; align-items: center; justify-content: center;
    gap: 6px; white-space: nowrap;
    box-shadow: 0 2px 2px rgba(0,0,0,0.05);
}
.btn:hover { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
.btn:active { transform: translateY(1px); }
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    background-color: #e65f0e;
}
.btn-secondary { background-color: #6c757d; color: white; }
.btn-secondary:hover { background-color: #5a6268; }

.btn-danger { background-color: #dc3545; color: white; }
.btn-danger:hover { background-color: #c82333; }

.btn-outline {
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    background-color: transparent;
}
.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Fix for Batch Delete Button */
.btn-outline.btn-danger {
    color: white;
    background-color: #dc3545;
    border-color: #dc3545;
}
.btn-outline.btn-danger:hover {
    color: white;
    background-color: #c82333;
    border-color: #c82333;
}


.btn-icon-page { /* Specific for table action icons */
    padding: 7px 9px; /* Slightly adjusted padding */
    background-color: transparent;
    color: var(--muted-color);
    border: 1px solid transparent; /* Make border transparent by default */
    border-radius: 6px;
}
.btn-icon-page:hover {
    background-color: var(--surface-alt);
    border-color: var(--border-color);
    color: var(--text-color);
}
.btn-icon-page.btn-danger:hover { /* Keep danger color on hover for delete */
    color: #dc3545;
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}
.btn-icon-page .icon { font-size: 16px; }

/* Cards Layout Styles */
.prompt-cards-container {
    width: 100%;
}

.cards-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 0 4px;
}

.select-all-container {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--muted-color);
}

.select-all-container input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.prompt-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    padding: 4px;
}

.prompt-card {
    background-color: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    min-height: 200px;
}

.prompt-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.prompt-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.card-checkbox {
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin-left: 8px;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.card-header-left {
    flex: 1;
    min-width: 0;
}

.card-header-right {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-date {
    font-size: 12px;
    color: var(--muted-color);
    margin: 0;
}

.card-content {
    margin-bottom: 16px;
}

.card-preview {
    font-size: 14px;
    color: var(--muted-color);
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.card-tags {
    margin-top: auto;
    min-height: 24px;
    padding-top: 12px;
}

.card-tags .tag {
    background-color: var(--surface-alt);
    color: var(--text-color);
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    margin-bottom: 6px;
    display: inline-block;
    border: 1px solid var(--border-color);
}

.card-actions {
    display: flex;
    gap: 4px;
}

.card-actions .btn-icon-page {
    margin-left: 5px; /* Spacing between action icons */
}

/* Modal Styles (copied and adapted from management page UI design) */
.modal {
    display: none; position: fixed; z-index: 1000; left: 0; top: 0;
    width: 100%; height: 100%; overflow: auto; background-color: rgba(0,0,0,0.5);
    align-items: center; justify-content: center;
}
.modal-content {
    background-color: var(--surface); margin: auto; padding: 25px 30px; border-radius: 8px;
    width: 90%; max-width: 600px; box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    animation: slideDownModal 0.3s ease-out;
}
@keyframes slideDownModal { /* Renamed animation to avoid conflict if any */
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
.modal-header {
    display: flex; justify-content: space-between; align-items: center;
    margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color);
}
.modal-header h2 { margin: 0; font-size: 20px; font-weight: 600; color: var(--text-color); }
.close-btn {
    color: #aaa; font-size: 28px; font-weight: bold; cursor: pointer;
    background: none; border: none; padding: 0; line-height: 1;
}
.close-btn:hover, .close-btn:focus { color: var(--text-color); }

.modal .form-group { margin-bottom: 20px; }
.modal .form-group label {
    display: block; font-size: 14px; font-weight: 500; margin-bottom: 8px; color: var(--muted-color);
}
.modal .form-group input[type="text"],
.modal .form-group textarea {
    width: calc(100% - 28px);
    padding: 12px 14px; border: 1px solid var(--border-color); border-radius: 6px;
    font-size: 14px; outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    white-space: pre-wrap; /* Ensure textarea respects newlines */
}
.modal .form-group input[type="text"]::placeholder,
.modal .form-group textarea::placeholder { color: var(--muted-color); }
.modal .form-group input[type="text"]:focus,
.modal .form-group textarea:focus {
    border-color: var(--primary-color); box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
}
.modal .form-group textarea { min-height: 120px; resize: vertical; }
.modal-actions { display: flex; justify-content: flex-end; gap: 12px; margin-top: 25px; }
        
.empty-state-card {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--muted-color);
    background-color: var(--surface);
    border: 2px dashed var(--border-color);
    border-radius: 12px;
}
.empty-state-card .icon {
    font-size: 56px;
    margin-bottom: 20px;
    color: var(--border-color);
}
.empty-state-card p {
    font-size: 18px;
    margin-bottom: 25px;
}

.icon { width: 16px; height: 16px; }

html.theme-dark img.icon {
    filter: invert(1) brightness(1.2);
}

.toast-notification { /* Shared toast style, ensure IDs are unique if both on same page */
    position: fixed; bottom: -50px; left: 50%; transform: translateX(-50%);
    background-color: #333; color: white; padding: 10px 20px; border-radius: 6px;
    font-size: 14px; z-index: 2000; opacity: 0;
    transition: opacity 0.3s ease, bottom 0.3s ease; visibility: hidden;
}
.toast-notification.show { opacity: 1; bottom: 30px; visibility: visible; }

.view-content {
    white-space: pre-wrap;
    word-break: break-word;
}

/* 通用图片样式 */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* 针对图标的样式 */
.inline-icon {
    width: 24px;
    height: 24px;
    vertical-align: middle;
}