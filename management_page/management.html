<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI提示词管理 - PromptMate</title>
    <link rel="stylesheet" href="management.css">
    
    <script>
      (function() {
        try {
          const theme = localStorage.getItem('theme');
          if (theme === 'dark') {
            document.documentElement.classList.add('theme-dark');
            // 立即设置body的背景色，防止闪白
            document.documentElement.style.backgroundColor = '#1e1e1e';
          } else {
            // 确保移除dark主题类
            document.documentElement.classList.remove('theme-dark');
            document.documentElement.style.backgroundColor = '#f8f9fa';
          }
        } catch (e) {
          // 如果localStorage不可用，使用默认浅色主题
          document.documentElement.style.backgroundColor = '#f8f9fa';
        }
      })();
    </script>

</head>
<body>
<div class="container">
        <header class="page-header">
            <h1>AI 提示词管理 <img src="../icons/PromptMate.png" alt="PromptMate Icon" class="inline-icon"></h1>
            <div class="header-actions">
                <button class="btn btn-primary" id="openAddModalBtn">
                    <img src="../icons/plus.svg" class="icon" alt="add"> 添加新提示词
                </button>
                <button class="btn btn-icon btn-header-action" id="themeToggleBtn" title="切换主题">
                    <img src="../icons/moon.svg" class="icon" alt="theme">
                </button>
            </div>
        </header>

        <div class="actions-bar">
            <input type="search" class="search-input-page" id="managementSearchInput" placeholder="搜索标题、内容或标签...">
            <div class="filter-controls">
                <select id="sortOrder">
                    <option value="date_desc">按日期降序</option>
                    <option value="date_asc">按日期升序</option>
                    <option value="title_asc">按标题升序 (A-Z)</option>
                    <option value="title_desc">按标题降序 (Z-A)</option>
                </select>
            </div>
            <div class="import-export-actions">
                <input type="file" id="importFileInput" accept=".json,.csv" style="display: none;">
                <button class="btn btn-outline" id="importBtn">
                    <img src="../icons/upload.svg" class="icon" alt="import"> 导入数据
                </button>
                <div class="export-group">
                    <button class="btn btn-outline" id="exportBtn">
                        <img src="../icons/download.svg" class="icon" alt="export"> 导出数据
                    </button>
                    <select id="exportFormat" class="export-format-select">
                        <option value="json">JSON 格式</option>
                        <option value="csv">CSV 格式</option>
                    </select>
                </div>
            </div>
            <button class="btn btn-outline btn-danger" id="batchDeleteBtn" style="display: none;" title="批量删除选中的提示词">
                <img src="../icons/trash.svg" class="icon" alt="delete"> 批量删除
            </button>
        </div>

        <div class="prompt-cards-container">
            <div class="cards-header">
                <div class="select-all-container">
                    <input type="checkbox" id="selectAllCheckbox" title="全选/取消全选">
                    <label for="selectAllCheckbox">全选</label>
                </div>
            </div>
            <div class="prompt-cards-grid" id="promptCardsGrid">
            </div>
            <div id="emptyStateCard" class="empty-state-card" style="display: none;">
                <div class="icon">✨</div>
                <p>还没有任何提示词，快去添加吧！</p>
                <button class="btn btn-primary" id="addFirstPromptModalBtnTable">添加第一个提示词</button>
            </div>
        </div>
    </div>

    <div id="promptModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">添加新提示词</h2>
                <button class="close-btn" id="closeModalBtn" title="关闭"><img src="../icons/close.svg" class="icon" alt="close"></button>
            </div>
            <div class="form-group">
                <label for="promptTitleInputModal">标题</label>
                <input type="text" id="promptTitleInputModal" placeholder="例如：生成周报摘要">
            </div>
            <div class="form-group">
                <label for="promptSummaryInputModal">提示词简介 (可选)</label>
                <input type="text" id="promptSummaryInputModal" placeholder="一句话概括此提示词">
            </div>
            <div class="form-group">
                <label for="promptContentInputModal">提示词内容</label>
                <textarea id="promptContentInputModal" rows="7" placeholder="输入你的提示词..."></textarea>
            </div>
            <div class="form-group">
                <label for="promptTagsInputModal">标签 (逗号分隔)</label>
                <input type="text" id="promptTagsInputModal" placeholder="例如：工作, 报告, 总结">
            </div>
            <div class="form-group">
                <label for="promptAuthorInputModal">作者 (可选)</label>
                <input type="text" id="promptAuthorInputModal" placeholder="作者姓名">
            </div>
            <div class="form-group">
                <label for="promptVersionInputModal">版本 (可选)</label>
                <input type="text" id="promptVersionInputModal" placeholder="例如：1.0">
            </div>
            <div class="form-group">
                <label for="promptNotesInputModal">备注 (可选)</label>
                <textarea id="promptNotesInputModal" rows="4" placeholder="关于此提示词的额外说明..."></textarea>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" id="cancelModalBtn">取消</button>
                <button class="btn btn-primary" id="saveModalBtn">保存</button>
            </div>
        </div>
    </div>
    <div id="toastNotificationManagement" class="toast-notification">操作成功！</div>
    <div id="viewPromptModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="viewPromptTitle">提示词详情</h2>
                <button class="close-btn" id="closeViewModalBtn" title="关闭"><img src="../icons/close.svg" class="icon" alt="close"></button>
            </div>
            <div class="modal-body">
                <p id="viewPromptMeta"></p>
                <pre id="viewPromptContent" class="view-content"></pre>
            </div>
        </div>
    </div>
    <script type="module" src="management.js"></script> 
</body>
</html>