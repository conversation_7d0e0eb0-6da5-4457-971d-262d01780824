document.addEventListener('DOMContentLoaded', async () => {
    // Import storage module
    const { promptStorage } = await import('../shared/storage.js');

    let currentPrompts = [];
    let displayedPrompts = [];
    let currentSortOrder = 'date_desc';
    let editingPromptId = null;

    // DOM Elements
    const promptCardsGrid = document.getElementById('promptCardsGrid');
    const emptyStateCard = document.getElementById('emptyStateCard');
    const addFirstPromptModalBtnTable = document.getElementById('addFirstPromptModalBtnTable');
    const modal = document.getElementById('promptModal');
    const openAddModalBtnPage = document.getElementById('openAddModalBtn');
    const closeModalBtn = document.getElementById('closeModalBtn');
    const modalTitle = document.getElementById('modalTitle');
    const promptTitleInputModal = document.getElementById('promptTitleInputModal');
    const promptSummaryInputModal = document.getElementById('promptSummaryInputModal');
    const promptContentInputModal = document.getElementById('promptContentInputModal');
    const promptTagsInputModal = document.getElementById('promptTagsInputModal');
    const promptAuthorInputModal = document.getElementById('promptAuthorInputModal');
    const promptVersionInputModal = document.getElementById('promptVersionInputModal');
    const promptNotesInputModal = document.getElementById('promptNotesInputModal');
    const viewPromptModal = document.getElementById('viewPromptModal');
    const closeViewModalBtn = document.getElementById('closeViewModalBtn');
    const viewPromptTitle = document.getElementById('viewPromptTitle');
    const viewPromptMeta = document.getElementById('viewPromptMeta');
    const viewPromptContent = document.getElementById('viewPromptContent');
    const cancelModalBtn = document.getElementById('cancelModalBtn');
    const saveModalBtn = document.getElementById('saveModalBtn');
    const searchInput = document.getElementById('managementSearchInput');
    const sortOrderSelect = document.getElementById('sortOrder');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const toastNotification = document.getElementById('toastNotificationManagement');
    const importBtn = document.getElementById('importBtn');
    const importFileInput = document.getElementById('importFileInput');
    const exportBtn = document.getElementById('exportBtn');

    let toastTimeout;

    // --- Utility Functions ---
    function showToast(message) {
        if (!toastNotification) return;
        if (toastTimeout) clearTimeout(toastTimeout);
        toastNotification.textContent = message;
        toastNotification.classList.add('show');
        toastTimeout = setTimeout(() => {
            toastNotification.classList.remove('show');
        }, 3000);
    }
    function escapeHTML(str) {
        if (typeof str !== 'string') return '';
        return str.replace(/[&<>"']/g, match =>
            ({ '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' }[match])
        );
    }
    function compareVersions(v1, v2) {
        const parts1 = (v1 || '0').split('.').map(s => parseInt(s, 10) || 0);
        const parts2 = (v2 || '0').split('.').map(s => parseInt(s, 10) || 0);
        const len = Math.max(parts1.length, parts2.length);
        for (let i = 0; i < len; i++) {
            const p1 = parts1[i] || 0;
            const p2 = parts2[i] || 0;
            if (p1 > p2) return 1;
            if (p1 < p2) return -1;
        }
        return 0;
    }

    // --- **** CORRECTED RENDER LOGIC **** ---
    function renderPromptTable() {
        // Clear only the prompt cards, leaving the container intact.
        promptCardsGrid.innerHTML = ''; 
    
        if (displayedPrompts.length === 0) {
            // If no prompts, hide the grid and show the empty state card.
            promptCardsGrid.style.display = 'none';
            emptyStateCard.style.display = 'block';
        } else {
            // If there are prompts, show the grid and hide the empty state card.
            promptCardsGrid.style.display = 'grid'; // Use 'grid' or your default display style
            emptyStateCard.style.display = 'none';
    
            displayedPrompts.forEach(prompt => {
                const card = document.createElement('div');
                card.className = 'prompt-card';
                card.dataset.id = prompt.id;
                const tagsHTML = prompt.tags && prompt.tags.length > 0
                    ? prompt.tags.map(tag => `<span class="tag">${escapeHTML(tag)}</span>`).join('')
                    : '<span class="tag" style="opacity: 0.5;">无标签</span>';
                const timeDate = prompt.time ? new Date(prompt.time).toLocaleDateString() : 'N/A';
                const previewSource = prompt.summary || prompt.promptContent || '';
                const previewText = previewSource.substring(0, 100) + (previewSource.length > 100 ? "..." : "");
    
                card.innerHTML = `
                    <div class="card-header">
                        <div class="card-header-left">
                            <h3 class="card-title" title="${escapeHTML(prompt.title)}">${escapeHTML(prompt.title)}</h3>
                            <p class="card-date">${timeDate}</p>
                        </div>
                        <div class="card-header-right">
                            <div class="card-actions">
                                <button class="btn btn-icon-page copy-table-btn" title="复制"><img src="../icons/copy.svg" class="icon" alt="copy"></button>
                                <button class="btn btn-icon-page edit-table-btn" title="编辑"><img src="../icons/edit.svg" class="icon" alt="edit"></button>
                                <button class="btn btn-icon-page btn-danger delete-table-btn" title="删除"><img src="../icons/trash.svg" class="icon" alt="delete"></button>
                            </div>
                            <input type="checkbox" class="card-checkbox row-checkbox" data-id="${prompt.id}">
                        </div>
                    </div>
                    <div class="card-content">
                        <p class="card-preview" title="${escapeHTML(previewSource)}">${escapeHTML(previewText)}</p>
                    </div>
                    <div class="card-tags">${tagsHTML}</div>`;
                promptCardsGrid.appendChild(card);
            });
        }
        updateBatchDeleteButtonVisibility();
        selectAllCheckbox.checked = false;
    }

    // --- Table Rendering, Sorting, Filtering ---
    function filterAndSortPrompts() {
        const searchTerm = searchInput.value.toLowerCase();
        let tempPrompts = [...currentPrompts];

        if (searchTerm) {
            tempPrompts = tempPrompts.filter(prompt =>
                (prompt.title && prompt.title.toLowerCase().includes(searchTerm)) ||
                (prompt.promptContent && prompt.promptContent.toLowerCase().includes(searchTerm)) ||
                (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
            );
        }
        displayedPrompts = promptStorage.sortPrompts(tempPrompts, currentSortOrder);
        renderPromptTable();
    }
    
    // --- Modal Control ---
    function openPromptModal(prompt = {}) {
        editingPromptId = prompt.id || null;
        modalTitle.textContent = prompt.id ? '编辑提示词' : '添加新提示词';
        promptTitleInputModal.value = prompt.title || '';
        promptSummaryInputModal.value = prompt.summary || '';
        promptContentInputModal.value = prompt.promptContent || '';
        promptTagsInputModal.value = prompt.tags ? prompt.tags.join(', ') : '';
        promptAuthorInputModal.value = prompt.author || '';
        promptVersionInputModal.value = prompt.version || '';
        promptNotesInputModal.value = prompt.notes || '';
        modal.style.display = 'flex';
        promptTitleInputModal.focus();
    }
    function closeModal() {
        modal.style.display = 'none';
    }
    function openViewPromptModal(prompt) {
        viewPromptTitle.textContent = prompt.title || '';
        const metaParts = [];
        if (prompt.author) metaParts.push(prompt.author);
        if (prompt.version) metaParts.push('V' + prompt.version);
        viewPromptMeta.textContent = metaParts.join(' - ');
        viewPromptContent.textContent = prompt.promptContent || '';
        viewPromptModal.style.display = 'flex';
    }
    function closeViewPromptModal() {
        viewPromptModal.style.display = 'none';
    }

    // --- CRUD and Data Handling ---
    async function handleSavePrompt() {
        const title = promptTitleInputModal.value.trim();
        const content = promptContentInputModal.value.trim();
        if (!title || !content) {
            showToast("标题和提示词内容不能为空！"); return;
        }
        const promptData = {
            title,
            promptContent: content,
            summary: promptSummaryInputModal.value.trim(),
            tags: promptTagsInputModal.value.split(',').map(tag => tag.trim()).filter(Boolean),
            author: promptAuthorInputModal.value.trim(),
            version: promptVersionInputModal.value.trim(),
            notes: promptNotesInputModal.value.trim(),
        };
        try {
            if (editingPromptId) {
                await promptStorage.updatePrompt(editingPromptId, promptData);
                showToast("提示词已更新！");
            } else {
                await promptStorage.addPrompt(promptData);
                showToast("提示词已添加！");
            }
            await initializeApp();
            closeModal();
        } catch (error) {
            showToast("保存失败: " + error.message);
        }
    }
    async function handleDeletePrompt(promptId) {
        const promptToDelete = currentPrompts.find(p => p.id === promptId);
        if (promptToDelete && confirm(`确定要删除提示词 "${promptToDelete.title}" 吗？`)) {
            await promptStorage.deletePrompt(promptId);
            await initializeApp();
            showToast("提示词已删除！");
        }
    }
    function updateBatchDeleteButtonVisibility() {
        const selectedCount = document.querySelectorAll('#promptCardsGrid .row-checkbox:checked').length;
        batchDeleteBtn.style.display = selectedCount > 0 ? 'inline-flex' : 'none';
    }
    async function handleBatchDelete() {
        const selectedCheckboxes = document.querySelectorAll('#promptCardsGrid .row-checkbox:checked');
        if (selectedCheckboxes.length === 0) return;
        if (confirm(`确定要删除选中的 ${selectedCheckboxes.length} 个提示词吗？`)) {
            const idsToDelete = Array.from(selectedCheckboxes).map(cb => cb.dataset.id);
            await promptStorage.deletePrompts(idsToDelete);
            await initializeApp();
            showToast(`${idsToDelete.length} 个提示词已删除！`);
        }
    }

    // --- Import/Export Logic ---
    function convertToCSV(prompts) {
        const headers = ['id', 'title', 'summary', 'promptContent', 'tags', 'author', 'version', 'notes', 'time'];
        return [
            headers.join(','),
            ...prompts.map(p => headers.map(h => `"${String(p[h] || '').replace(/"/g, '""')}"`).join(','))
        ].join('\n');
    }
    async function handleExportData() {
        const promptsToExport = await promptStorage.loadPrompts();
        if (promptsToExport.length === 0) {
            showToast("没有可导出的提示词。"); return;
        }
        const format = document.getElementById('exportFormat').value;
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `PromptMateData_${timestamp}.${format}`;
        const mimeType = format === 'csv' ? 'text/csv;charset=utf-8' : 'application/json';
        const content = format === 'csv' ? convertToCSV(promptsToExport) : JSON.stringify(promptsToExport, null, 2);
        
        const blob = new Blob([content], { type: mimeType });
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = filename;
        a.click();
        URL.revokeObjectURL(a.href);
    }
    function parseCSV(text) {
        const lines = text.trim().split('\n');
        const headers = lines.shift().split(',').map(h => h.trim().replace(/^"|"$/g, ''));
        return lines.map(line => {
            const values = line.split(',').map(v => v.trim().replace(/^"|"$/g, '').replace(/""/g, '"'));
            const obj = {};
            headers.forEach((h, i) => {
                obj[h] = values[i] || '';
                if (h === 'tags' && obj[h]) obj[h] = obj[h].split(';').map(t => t.trim()).filter(Boolean);
            });
            return obj;
        });
    }
    if (importFileInput) {
        importFileInput.addEventListener('change', async (event) => {
            const file = event.target.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const text = e.target.result;
                    const importedData = file.name.endsWith('.json') ? JSON.parse(text) : parseCSV(text);
                    if (!Array.isArray(importedData)) throw new Error("无效数据格式");
                    
                    const localPromptsMap = new Map((await promptStorage.loadPrompts()).map(p => [p.id, p]));
                    let newCount = 0, updatedCount = 0, skippedCount = 0;

                    for (const p of importedData) {
                        if (!p.id || !p.title) continue;
                        const existing = localPromptsMap.get(p.id);
                        if (!existing) {
                            localPromptsMap.set(p.id, p); newCount++;
                        } else if (compareVersions(p.version, existing.version) > 0) {
                            localPromptsMap.set(p.id, { ...existing, ...p }); updatedCount++;
                        } else {
                            skippedCount++;
                        }
                    }
                    
                    await promptStorage.savePrompts(Array.from(localPromptsMap.values()));
                    await initializeApp();
                    showToast(`导入完成: ${newCount} 新增, ${updatedCount} 更新, ${skippedCount} 跳过.`);
                } catch (error) {
                    showToast(`导入失败: ${error.message}`);
                } finally {
                    importFileInput.value = '';
                }
            };
            reader.readAsText(file, 'utf-8');
        });
    }

    // --- Event Listeners Setup ---
    openAddModalBtnPage.addEventListener('click', () => openPromptModal());
    addFirstPromptModalBtnTable.addEventListener('click', () => openPromptModal());
    closeModalBtn.addEventListener('click', closeModal);
    cancelModalBtn.addEventListener('click', closeModal);
    saveModalBtn.addEventListener('click', handleSavePrompt);
    modal.addEventListener('click', (event) => { if (event.target === modal) closeModal(); });
    closeViewModalBtn.addEventListener('click', closeViewPromptModal);
    viewPromptModal.addEventListener('click', (e) => { if (e.target === viewPromptModal) closeViewPromptModal(); });
    promptCardsGrid.addEventListener('click', (event) => {
        const cardElement = event.target.closest('.prompt-card[data-id]');
        if (!cardElement) return;
        const promptId = cardElement.dataset.id;
        const promptData = currentPrompts.find(p => p.id === promptId);
        if (!promptData) return;
        if (event.target.closest('.copy-table-btn')) {
            navigator.clipboard.writeText(promptData.promptContent).then(() => showToast("已复制到剪贴板！"));
        } else if (event.target.closest('.edit-table-btn')) {
            openPromptModal(promptData);
        } else if (event.target.closest('.delete-table-btn')) {
            handleDeletePrompt(promptId);
        } else if (event.target.classList.contains('row-checkbox')) {
            updateBatchDeleteButtonVisibility();
        } else {
            openViewPromptModal(promptData);
        }
    });
    searchInput.addEventListener('input', filterAndSortPrompts);
    sortOrderSelect.addEventListener('change', (e) => { currentSortOrder = e.target.value; filterAndSortPrompts(); });
    selectAllCheckbox.addEventListener('change', (e) => {
        promptCardsGrid.querySelectorAll('.row-checkbox').forEach(cb => cb.checked = e.target.checked);
        updateBatchDeleteButtonVisibility();
    });
    batchDeleteBtn.addEventListener('click', handleBatchDelete);
    exportBtn.addEventListener('click', handleExportData);
    importBtn.addEventListener('click', () => importFileInput.click());

    // --- Initialization & Storage Sync ---
    async function initializeApp() {
        currentPrompts = await promptStorage.loadPrompts();
        filterAndSortPrompts();
    }
    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local') {
            initializeApp();
            showToast("提示词列表已同步更新。");
        }
    });
    const themeToggleBtn = document.getElementById('themeToggleBtn');
    function applyTheme() {
        const isDark = localStorage.getItem('theme') === 'dark';
        document.documentElement.classList.toggle('theme-dark', isDark);
        // 清理内联样式，让CSS变量接管
        document.documentElement.style.backgroundColor = '';
        if (themeToggleBtn) {
            themeToggleBtn.querySelector('img').src = isDark ? '../icons/sun.svg' : '../icons/moon.svg';
        }
    }
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            const isDark = !document.documentElement.classList.contains('theme-dark');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
            applyTheme();
        });
    }
    window.addEventListener('storage', (e) => { if (e.key === 'theme') applyTheme(); });

    initializeApp();
    applyTheme();
});