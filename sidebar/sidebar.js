document.addEventListener('DOMContentLoaded', async () => {
    // Import storage module and config
    const { promptStorage } = await import('../shared/storage.js');
    const { STORAGE_CONFIG } = await import('../shared/config.js');

    // Storage key constant
    const PROMPT_PAL_STORAGE_KEY = STORAGE_CONFIG.STORAGE_KEY;
    const PENDING_PROMPT_SESSION_KEY = 'pendingPromptFromContextMenu';
    let currentPrompts = [];
    let pendingActionType = null;
    let currentFormTags = new Set();
    let allAvailableTags = new Set();

    // --- DOM Elements ---
    const promptListContainer = document.getElementById('promptList');
    const emptyStateView = document.getElementById('emptyStateView');
    const searchInput = document.getElementById('searchInput');
    const mainView = document.getElementById('promptPalMainView');
    const addEditView = document.getElementById('addEditPromptView');
    const showAddFormBtn = document.getElementById('showAddFormBtn');
    const addFirstPromptBtn = document.getElementById('addFirstPromptBtn');
    const backToListBtn = document.getElementById('backToListBtn');
    const cancelFormBtnSidebar = document.getElementById('cancelFormBtnSidebar');
    const saveFormBtnSidebar = document.getElementById('saveFormBtnSidebar');
    const formTitleSidebar = document.getElementById('formTitleSidebar');
    const promptTitleInput = document.getElementById('promptTitleInputSidebar');
    const promptSummaryInput = document.getElementById('promptSummaryInputSidebar');
    const promptContentInput = document.getElementById('promptContentInputSidebar');
    const promptAuthorInput = document.getElementById('promptAuthorInputSidebar');
    const promptVersionInput = document.getElementById('promptVersionInputSidebar');
    const promptNotesInput = document.getElementById('promptNotesInputSidebar');
    const toastNotification = document.getElementById('toastNotification');
    const openManagementPageBtn = document.getElementById('openManagementPageBtn');
    const viewPromptModal = document.getElementById('viewPromptModal');
    const closeViewModalBtn = document.getElementById('closeViewModalBtn');
    const viewPromptTitle = document.getElementById('viewPromptTitle');
    const viewPromptMeta = document.getElementById('viewPromptMeta');
    const viewPromptContent = document.getElementById('viewPromptContent');
    const tagsInputContainer = document.getElementById('tagsInputContainerSidebar');
    const tagInputField = document.getElementById('tagInputFieldSidebar');
    const tagSuggestionsContainer = document.getElementById('tagSuggestionsSidebar');

    let toastTimeout;

    // --- Utility Functions ---
    function showToast(message) {
        if (!toastNotification) return;
        if (toastTimeout) clearTimeout(toastTimeout);
        toastNotification.textContent = message;
        toastNotification.classList.add('show');
        toastTimeout = setTimeout(() => {
            toastNotification.classList.remove('show');
        }, 2500);
    }
    function htmlToPlainTextWithNewlines(html) {
        if (typeof html !== 'string') return '';
        let tempDiv = document.createElement("div");
        html = html.replace(/<br\s*\/?>/gi, '{{NEWLINE_PLACEHOLDER}}');
        const blockTags = ['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'pre', 'blockquote', 'dd', 'dt', 'hr', 'header', 'footer', 'article', 'section', 'aside', 'nav', 'figure', 'figcaption', 'table', 'tr', 'th', 'td', 'ul', 'ol'];
        blockTags.forEach(tag => {
            const regexClose = new RegExp(`</${tag}>`, 'gi');
            html = html.replace(regexClose, '{{NEWLINE_PLACEHOLDER}}');
        });
        tempDiv.innerHTML = html;
        let text = tempDiv.textContent || tempDiv.innerText || "";
        text = text.replace(/{{NEWLINE_PLACEHOLDER}}/g, '\n');
        text = text.replace(/\n\s*\n/g, '\n\n').trim();
        return text.split('\n').map(line => line.trim()).join('\n').trim();
    }
    function escapeHTMLForDisplay(str) {
        if (typeof str !== 'string') return '';
        let displayStr = str.replace(/\n/g, '<br>');
        return displayStr.replace(/[&<>"']/g, match =>
            ({ '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;' }[match])
        );
    }
    function prepareTextForTextarea(str) {
        if (typeof str !== 'string') return '';
        return str;
    }
    function switchToView(viewToShow) {
        document.querySelectorAll('.view').forEach(view => view.classList.remove('active-view'));
        if (viewToShow) viewToShow.classList.add('active-view');
    }
    function openViewPromptModal(prompt) {
        if (!viewPromptModal || !viewPromptTitle || !viewPromptContent) return;
        viewPromptTitle.textContent = prompt.title || '';
        const metaParts = [];
        if (prompt.author) metaParts.push(prompt.author);
        if (prompt.version) metaParts.push('V' + prompt.version);
        viewPromptMeta.textContent = metaParts.join(' - ');
        viewPromptContent.textContent = prompt.promptContent || '';
        viewPromptModal.style.display = 'flex';
    }
    function closeViewPromptModal() {
        if (viewPromptModal) viewPromptModal.style.display = 'none';
    }

    // --- Storage Functions ---
    async function loadAndRenderPrompts() {
        try {
            currentPrompts = await promptStorage.loadPrompts();
            updateAllAvailableTags(currentPrompts);
            renderPrompts(currentPrompts);
        } catch (error) {
            showToast("加载提示词失败！");
        }
    }
    
    // --- Tag Input Logic ---
    function updateAllAvailableTags(prompts) {
        allAvailableTags.clear();
        prompts.forEach(prompt => {
            if (prompt.tags && prompt.tags.length > 0) {
                prompt.tags.forEach(tag => allAvailableTags.add(tag.toLowerCase()));
            }
        });
    }
    function renderSelectedTags() {
        if (!tagsInputContainer || !tagInputField) return;
        tagsInputContainer.querySelectorAll('.tag-item').forEach(el => el.remove());

        currentFormTags.forEach(tagText => {
            const tagItem = document.createElement('span');
            tagItem.className = 'tag-item';
            tagItem.textContent = escapeHTMLForDisplay(tagText);
            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-tag-btn';
            removeBtn.innerHTML = '&times;';
            removeBtn.type = 'button';
            removeBtn.addEventListener('click', () => {
                currentFormTags.delete(tagText);
                renderSelectedTags();
                tagInputField.focus();
            });
            tagItem.appendChild(removeBtn);
            tagsInputContainer.insertBefore(tagItem, tagInputField);
        });
    }
    function addTagFromInput() {
        if (!tagInputField) return;
        const tagText = tagInputField.value.trim();
        if (tagText && !currentFormTags.has(tagText)) {
            currentFormTags.add(tagText);
            renderSelectedTags();
        }
        tagInputField.value = '';
        hideTagSuggestions();
    }
    function showTagSuggestions(query) {
        if (!tagSuggestionsContainer || !allAvailableTags) return;
        tagSuggestionsContainer.innerHTML = '';
        if (!query) {
            hideTagSuggestions();
            return;
        }
        const lowerQuery = query.toLowerCase();
        const suggestions = Array.from(allAvailableTags).filter(tag =>
            tag.toLowerCase().includes(lowerQuery) && !currentFormTags.has(tag)
        ).slice(0, 5);

        if (suggestions.length > 0) {
            suggestions.forEach(suggestion => {
                const item = document.createElement('div');
                item.className = 'tag-suggestion-item';
                item.textContent = suggestion;
                item.addEventListener('click', () => {
                    currentFormTags.add(suggestion);
                    renderSelectedTags();
                    tagInputField.value = '';
                    hideTagSuggestions();
                    tagInputField.focus();
                });
                tagSuggestionsContainer.appendChild(item);
            });
            tagSuggestionsContainer.style.display = 'block';
        } else {
            hideTagSuggestions();
        }
    }
    function hideTagSuggestions() {
        if (tagSuggestionsContainer) tagSuggestionsContainer.style.display = 'none';
    }
    if (tagsInputContainer) tagsInputContainer.addEventListener('click', () => tagInputField.focus());
    if (tagInputField) {
        tagInputField.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ',' || e.key === ';') {
                e.preventDefault();
                addTagFromInput();
            } else if (e.key === "Backspace" && tagInputField.value === '' && currentFormTags.size > 0) {
                const lastTag = Array.from(currentFormTags).pop();
                currentFormTags.delete(lastTag);
                renderSelectedTags();
            }
        });
        tagInputField.addEventListener('input', (e) => showTagSuggestions(e.target.value));
        tagInputField.addEventListener('blur', () => {
            setTimeout(() => { if (tagInputField.value.trim()) addTagFromInput(); hideTagSuggestions(); }, 150);
        });
    }

    // --- Core Application Logic ---
    function renderPrompts(promptsToRender) {
        promptListContainer.innerHTML = '';
        if (promptsToRender.length === 0) {
            emptyStateView.style.display = 'flex';
            promptListContainer.appendChild(emptyStateView);
        } else {
            emptyStateView.style.display = 'none';
            promptsToRender.forEach(prompt => {
                const promptItemDiv = document.createElement('div');
                promptItemDiv.className = 'prompt-item';
                promptItemDiv.dataset.id = prompt.id;
                const tagsHTML = prompt.tags && prompt.tags.length > 0 ? prompt.tags.map(tag => `<span class="tag">${escapeHTMLForDisplay(tag)}</span>`).join('') : '';
                const previewSource = prompt.summary || prompt.promptContent || '';
                const previewText = previewSource.substring(0, 100) + (previewSource.length > 100 ? "..." : "");
                promptItemDiv.innerHTML = `
                    <div class="prompt-item-header">
                        <span class="prompt-title">${escapeHTMLForDisplay(prompt.title)}</span>
                        <div class="prompt-actions">
                            <button class="btn btn-icon copy-btn" title="复制"><img src="../icons/copy.svg" class="icon" alt="copy"></button>
                            <button class="btn btn-icon edit-btn" title="编辑"><img src="../icons/edit.svg" class="icon" alt="edit"></button>
                            <button class="btn btn-icon delete-btn" title="删除"><img src="../icons/trash.svg" class="icon" alt="delete"></button>
                        </div>
                    </div>
                    <p class="prompt-content-preview">${escapeHTMLForDisplay(previewText)}</p>
                    <div class="prompt-tags">${tagsHTML}</div>`;
                promptListContainer.appendChild(promptItemDiv);
            });
        }
    }
    function openAddFormWithData(data) {
        formTitleSidebar.textContent = data.isEditing ? '编辑提示词' : (data.fromContextMenu ? '添加新提示词 (来自选择)' : '添加新提示词');
        promptTitleInput.value = data.title || '';
        promptSummaryInput.value = data.summary || '';
        let contentToSet = data.content || data.promptContent || '';
        if (data.isHtmlContent) {
            contentToSet = htmlToPlainTextWithNewlines(contentToSet);
        }
        promptContentInput.value = prepareTextForTextarea(contentToSet);
        currentFormTags.clear();
        if (data.tags) data.tags.forEach(tag => currentFormTags.add(tag));
        renderSelectedTags();
        tagInputField.value = '';
        promptAuthorInput.value = data.author || '';
        promptVersionInput.value = data.version || '';
        promptNotesInput.value = data.notes || '';
        promptTitleInput.dataset.editingId = data.isEditing ? data.id : '';
        pendingActionType = data.fromContextMenu ? data.actionType : null;
        switchToView(addEditView);
        promptTitleInput.focus();
    }
    async function checkForPendingPrompt() {
        try {
            const result = await chrome.storage.session.get([PENDING_PROMPT_SESSION_KEY]);
            if (result && result[PENDING_PROMPT_SESSION_KEY]) {
                const data = result[PENDING_PROMPT_SESSION_KEY];
                openAddFormWithData({
                    content: data.selectionHtml, actionType: data.actionType,
                    isEditing: false, fromContextMenu: true, isHtmlContent: data.isHtml
                });
                await chrome.storage.session.remove(PENDING_PROMPT_SESSION_KEY);
            }
        } catch (error) {
            console.error("Sidebar: Error checking for pending prompt:", error);
        }
    }
    async function initializeApp() {
        await loadAndRenderPrompts();
        await checkForPendingPrompt();
    }

    // --- Event Listeners Setup ---
    showAddFormBtn.addEventListener('click', () => openAddFormWithData({}));
    addFirstPromptBtn.addEventListener('click', () => showAddFormBtn.click());
    backToListBtn.addEventListener('click', () => switchToView(mainView));
    cancelFormBtnSidebar.addEventListener('click', () => switchToView(mainView));

    // --- REFACTORED AND CORRECTED SAVE LOGIC ---
    saveFormBtnSidebar.addEventListener('click', async () => {
        const title = promptTitleInput.value.trim();
        const content = promptContentInput.value.trim();
        if (!title || !content) {
            showToast("标题和提示词内容不能为空！"); return;
        }

        const promptData = {
            title: title,
            promptContent: content,
            summary: promptSummaryInput.value.trim(),
            tags: Array.from(currentFormTags),
            author: promptAuthorInput.value.trim(),
            version: promptVersionInput.value.trim(),
            notes: promptNotesInput.value.trim(),
        };

        const editingId = promptTitleInput.dataset.editingId;

        try {
            if (editingId) {
                // Use the safe update method from storage module
                currentPrompts = await promptStorage.updatePrompt(editingId, promptData);
                showToast("提示词已更新！");
            } else {
                // Use the safe add method from storage module
                currentPrompts = await promptStorage.addPrompt(promptData);
                showToast("提示词已添加！");
            }

            // The returned `currentPrompts` is the full, updated list.
            renderPrompts(currentPrompts);
            updateAllAvailableTags(currentPrompts);
            switchToView(mainView);

            if (pendingActionType === 'collectAndOpenManagement') {
                showToast("提示词已保存，将打开管理页面...");
                chrome.tabs.create({ url: chrome.runtime.getURL('management_page/management.html') });
            }
            pendingActionType = null;

        } catch (error) {
            showToast("保存失败：" + error.message);
        }
    });

    promptListContainer.addEventListener('click', async (event) => {
        const targetButton = event.target.closest('button.btn-icon');
        const promptItemDiv = event.target.closest('.prompt-item');
        if (!promptItemDiv) return;
        const promptId = promptItemDiv.dataset.id;
        const promptData = currentPrompts.find(p => p.id === promptId);
        if (!promptData) return;

        if (targetButton?.classList.contains('copy-btn')) {
            event.stopPropagation();
            await navigator.clipboard.writeText(promptData.promptContent);
            showToast('已复制到剪贴板！');
        } else if (targetButton?.classList.contains('edit-btn')) {
            event.stopPropagation();
            openAddFormWithData({ ...promptData, isEditing: true });
        } else if (targetButton?.classList.contains('delete-btn')) {
            event.stopPropagation();
            if (confirm(`确定要删除提示词 "${promptData.title}" 吗？`)) {
                currentPrompts = await promptStorage.deletePrompt(promptId);
                renderPrompts(currentPrompts); // Re-render the list
                showToast('提示词已删除！');
            }
        } else if (!targetButton) {
            openViewPromptModal(promptData);
        }
    });

    searchInput.addEventListener('input', (event) => {
        const searchTerm = event.target.value.toLowerCase();
        const filteredPrompts = currentPrompts.filter(prompt =>
            (prompt.title.toLowerCase().includes(searchTerm) ||
             prompt.promptContent.toLowerCase().includes(searchTerm) ||
             (prompt.tags && prompt.tags.some(tag => tag.toLowerCase().includes(searchTerm))))
        );
        renderPrompts(filteredPrompts);
    });

    openManagementPageBtn.addEventListener('click', () => {
        chrome.tabs.create({ url: chrome.runtime.getURL('management_page/management.html') });
    });

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.type === 'NEW_PROMPT_FROM_CONTEXT_MENU' && message.data) {
            openAddFormWithData({
                content: message.data.selectionHtml, actionType: message.data.actionType,
                isEditing: false, fromContextMenu: true, isHtmlContent: message.data.isHtml
            });
            chrome.storage.session.remove(PENDING_PROMPT_SESSION_KEY);
            sendResponse({ success: true });
        }
        return true;
    });

    chrome.storage.onChanged.addListener((changes, namespace) => {
        if (namespace === 'local' && changes[PROMPT_PAL_STORAGE_KEY]) {
            loadAndRenderPrompts(); // Simply reload all data to ensure sync
            showToast("提示词列表已同步更新。");
        }
    });

    const themeToggleBtn = document.getElementById('themeToggleBtn');
    function applyTheme() {
        const saved = localStorage.getItem('theme');
        const isDark = saved === 'dark';
        document.documentElement.classList.toggle('theme-dark', isDark);
        if (themeToggleBtn) {
            themeToggleBtn.querySelector('img').src = isDark ? '../icons/sun.svg' : '../icons/moon.svg';
        }
    }
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            const nowDark = !document.documentElement.classList.contains('theme-dark');
            document.documentElement.classList.toggle('theme-dark', nowDark);
            localStorage.setItem('theme', nowDark ? 'dark' : 'light');
            themeToggleBtn.querySelector('img').src = nowDark ? '../icons/sun.svg' : '../icons/moon.svg';
        });
    }

    window.addEventListener('storage', (e) => { if (e.key === 'theme') applyTheme(); });
    closeViewModalBtn.addEventListener('click', closeViewPromptModal);
    viewPromptModal.addEventListener('click', (e) => { if (e.target === viewPromptModal) closeViewPromptModal(); });

    initializeApp();
    applyTheme();
});