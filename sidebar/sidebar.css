:root {
    --primary-color: #FF6F10;
    --background: #f8f9fa;
    --surface: #ffffff;
    --surface-alt: #f8f9fa;
    --text-color: #333333;
    --muted-color: #495057;
    --border-color: #dee2e6;
}

.theme-dark {
    --primary-color: #FF6F10;
    --background: #1e1e1e;
    --surface: #2e2e2e;
    --surface-alt: #252525;
    --text-color: #f0f0f0;
    --muted-color: #cccccc;
    --border-color: #555555;
    color-scheme: dark;
}

body {
    font-family: 'Inter', sans-serif;
    margin: 0;
    background-color: var(--background); /* Sidebar background */
    color: var(--text-color);
    font-size: 14px;
    height: 100vh; /* Full height for sidebar */
    width: 100%;   /* Full width of the sidebar panel */
    overflow: hidden;
}

.prompt-pal-sidebar { /* Renamed from .prompt-pal-popup */
    width: 100%;
    height: 100%;
    background-color: var(--surface);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.view {
    display: none; 
    flex-direction: column;
    width: 100%;
    height: 100%; 
    overflow: hidden; 
}
.view.active-view {
    display: flex; 
}

/* 头部区域 */
.sidebar-header { /* Renamed from .popup-header */
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--surface);
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
    flex-shrink: 0;
}

.search-input {
    flex-grow: 1;
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.search-input::placeholder { color: var(--muted-color); }
.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 16, 0.15);
}

.btn {
    padding: 10px 16px; border: none; border-radius: 8px; cursor: pointer;
    font-size: 14px; font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
    display: inline-flex; align-items: center; justify-content: center;
    gap: 6px; white-space: nowrap;
    box-shadow: 0 2px 2px rgba(0,0,0,0.05);
}
.btn:hover { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
.btn:active { transform: translateY(1px); }
.btn-primary { background-color: var(--primary-color); color: white; }
.btn-primary:hover { background-color: #e65f0e; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-secondary:hover { background-color: #5a6268; }

.btn-icon {
    padding: 8px; background-color: transparent; color: var(--muted-color);
    border: none; border-radius: 50%; line-height: 1; 
    display: inline-flex; align-items: center; justify-content: center;
}
.btn-icon:hover { background-color: var(--surface-alt); color: var(--primary-color); }
.btn-icon .icon { font-size: 18px; }
.btn-header-action { padding: 9px; font-size: 16px; }

/* 提示词列表区域 */
.prompt-list-container {
    flex-grow: 1; 
    overflow-y: auto; 
    padding: 12px 16px; 
    display: flex;
    flex-direction: column;
    gap: 12px;
}
.prompt-list-container::-webkit-scrollbar { width: 6px; }
.prompt-list-container::-webkit-scrollbar-track { background: var(--surface-alt); }
.prompt-list-container::-webkit-scrollbar-thumb { background-color: var(--border-color); border-radius: 3px; }
.prompt-list-container::-webkit-scrollbar-thumb:hover { background-color: var(--muted-color); }

.prompt-item {
    background-color: var(--surface);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
    min-height: 140px;
    display: flex;
    flex-direction: column;
}
.prompt-item:hover {
    background-color: var(--surface-alt);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(255, 111, 16, 0.15);
    transform: translateY(-1px);
}
.prompt-item:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prompt-item-header {
    display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;
}
.prompt-title {
    font-size: 16px; font-weight: 600; color: var(--text-color);
    line-height: 1.4;
    flex: 1;
    margin-right: 12px;
    word-wrap: break-word;
}
.prompt-actions { 
    display: flex; gap: 4px; 
    opacity: 0.7;
    transition: opacity 0.2s ease;
    flex-shrink: 0;
}
.prompt-item:hover .prompt-actions {
    opacity: 1;
}
.prompt-content-preview {
    font-size: 13px; color: var(--muted-color); line-height: 1.4; 
    max-height: 54px;
    overflow: hidden; text-overflow: ellipsis; display: -webkit-box;
    -webkit-line-clamp: 3; -webkit-box-orient: vertical; 
    margin-bottom: auto;
    word-wrap: break-word;
    flex-shrink: 0;
}
.prompt-tags { 
    display: flex; flex-wrap: wrap; gap: 6px;
    min-height: 24px;
    margin-top: 8px;
    flex-shrink: 0;
}
.tag {
    background-color: var(--surface-alt); color: var(--muted-color); 
    padding: 4px 8px;
    border-radius: 8px; font-size: 11px; font-weight: 500;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}
.tag:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 添加/编辑表单视图 */
.form-view { background-color: var(--surface); }
.form-view-header {
    padding: 20px 20px 15px 20px; display: flex;
    justify-content: space-between; align-items: center;
    border-bottom: 1px solid var(--border-color); flex-shrink: 0;
}
.form-view-header h2 { font-size: 18px; font-weight: 600; color: var(--text-color); margin: 0; }
.form-view-header .btn-icon { color: var(--muted-color); }
.form-view-header .btn-icon:hover { color: var(--text-color); }

.form-content-scrollable {
    flex-grow: 1; overflow-y: auto; padding: 20px; 
}
.form-content-scrollable::-webkit-scrollbar { width: 6px; }
.form-content-scrollable::-webkit-scrollbar-track { background: var(--surface-alt); }
.form-content-scrollable::-webkit-scrollbar-thumb { background-color: var(--border-color); border-radius: 3px; }
.form-content-scrollable::-webkit-scrollbar-thumb:hover { background-color: var(--muted-color); }

.form-group { margin-bottom: 18px; }
.form-group:last-child { margin-bottom: 0; }
.form-group label {
    display: block; font-size: 13px; font-weight: 500; margin-bottom: 8px; color: var(--muted-color);
}
.form-group input[type="text"],
.form-group textarea {
    width: calc(100% - 28px); padding: 12px 14px; border: 1px solid var(--border-color);
    border-radius: 6px; font-size: 14px; outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.form-group input[type="text"]::placeholder,
.form-group textarea::placeholder { color: var(--muted-color); }
.form-group input[type="text"]:focus,
.form-group textarea:focus {
    border-color: var(--primary-color); box-shadow: 0 0 0 3px rgba(255, 111, 16, 0.15);
}
.form-group textarea { min-height: 100px; resize: vertical; }

.form-actions {
    display: flex; justify-content: flex-end; gap: 12px;
    padding: 15px 20px; flex-shrink: 0; border-top: 1px solid var(--border-color);
}

/* 空状态提示 */
.empty-state {
    text-align: center; 
    padding: 32px 20px; 
    color: var(--muted-color);
    display: flex; 
    flex-direction: column; 
    justify-content: center;
    align-items: center; 
    margin: 20px 16px;
    background-color: var(--surface);
    border: 2px dashed var(--border-color);
    border-radius: 16px;
    transition: all 0.2s ease;
}
.empty-state:hover {
    border-color: var(--primary-color);
    background-color: var(--surface-alt);
}
.empty-state .icon { 
    font-size: 48px; 
    margin-bottom: 16px; 
    color: var(--border-color);
    transition: color 0.2s ease;
}
.empty-state:hover .icon {
    color: var(--primary-color);
}
.empty-state p { 
    font-size: 16px; 
    margin-bottom: 20px; 
    line-height: 1.5;
}

.icon { width: 16px; height: 16px; }

html.theme-dark img.icon {
    filter: invert(1) brightness(1.2);
}

.toast-notification {
    position: fixed; bottom: -50px; left: 50%; transform: translateX(-50%);
    background-color: #333; color: white; padding: 10px 20px; border-radius: 6px;
    font-size: 14px; z-index: 2000; opacity: 0;
    transition: opacity 0.3s ease, bottom 0.3s ease; visibility: hidden;
}
.toast-notification.show { opacity: 1; bottom: 20px; visibility: visible; }

.tags-input-container {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: text; /* Change cursor to text when clicking on container */
}

.tags-input-container:focus-within { /* Style when container or its input has focus */
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 16, 0.15);
}

.tag-item {
    display: inline-flex;
    align-items: center;
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    margin-right: 6px;
    margin-bottom: 4px; /* For wrapping */
    font-size: 13px;
    font-weight: 500;
}

.tag-item .remove-tag-btn {
    margin-left: 6px;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    line-height: 1;
}
.tag-item .remove-tag-btn:hover {
    opacity: 0.8;
}

.tags-input-field {
    flex-grow: 1;
    border: none;
    outline: none;
    padding: 4px 0; /* Minimal padding */
    font-size: 14px;
    min-width: 100px; /* Minimum width for the input field */
    margin-bottom: 4px; /* Align with tag-items when wrapping */
}

.tag-suggestions {
    border: 1px solid var(--border-color);
    border-top: none; /* Attach to bottom of input container */
    border-radius: 0 0 6px 6px;
    max-height: 150px;
    overflow-y: auto;
    background-color: var(--surface);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.tag-suggestion-item {
    padding: 8px 12px;
    cursor: pointer;
    font-size: 13px;
}

.tag-suggestion-item:hover {
    background-color: var(--surface-alt);
}

/* Modal Styles (copied from management page) */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    align-items: center;
    justify-content: center;
}
.modal-content {
    background-color: var(--surface);
    margin: auto;
    padding: 25px 30px;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    animation: slideDownModal 0.3s ease-out;
}
@keyframes slideDownModal {
    from { transform: translateY(-30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}
.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
}
.close-btn {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    line-height: 1;
}
.close-btn:hover,
.close-btn:focus {
    color: var(--text-color);
}
.view-content {
    white-space: pre-wrap;
    word-break: break-word;
}