/* popup/popup.css */
:root {
    --primary-color: #FF6F10;
    --background: #f8f9fa;
    --surface: #ffffff;
    --surface-alt: #f8f9fa;
    --text-color: #333333;
    --muted-color: #495057;
    --border-color: #dee2e6;
}

.theme-dark {
    --primary-color: #FF6F10;
    --background: #1e1e1e;
    --surface: #2e2e2e;
    --surface-alt: #252525;
    --text-color: #f0f0f0;
    --muted-color: #cccccc;
    --border-color: #555555;
    color-scheme: dark;
}

body {
    font-family: 'Inter', sans-serif;
    margin: 0;
    background-color: var(--background);
    color: var(--text-color);
    font-size: 14px;
    width: 380px;
    max-height: 580px;
    overflow: hidden;
}

.prompt-pal-popup {
    width: 100%; 
    height: 100%;
    max-height: 580px; /* Ensure this matches body's max-height or is derived */
    background-color: var(--surface);
    display: flex;
    flex-direction: column;
    overflow: hidden; 
}

.view {
    display: none; 
    flex-direction: column;
    width: 100%;
    height: 100%; /* Each view tries to take full height of parent */
    overflow: hidden; /* Important for view itself not to scroll */
}
.view.active-view {
    display: flex; 
}


/* 头部区域 */
.popup-header {
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(to right, var(--primary-color), #5aa6ff);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    color: #fff;
    flex-shrink: 0;
}

.search-input {
    flex-grow: 1;
    padding: 10px 14px;
    border: 1px solid var(--border-color); 
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.search-input::placeholder {
    color: #adb5bd;
}
.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 16, 0.15);
}

.btn {
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    white-space: nowrap;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.05);
}
.btn:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    background-color: #e65f0e;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-outline {
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}
.btn-outline:hover {
    background-color: rgba(255, 111, 16, 0.05);
}

.btn-icon { 
    padding: 8px;
    background-color: transparent;
    color: #6c757d; 
    border: none;
    border-radius: 50%; 
    line-height: 1; 
    display: inline-flex; 
    align-items: center;
    justify-content: center;
}
.btn-icon:hover {
    background-color: #e9ecef;
    color: var(--primary-color);
}
.btn-icon .icon {
    font-size: 18px; 
}
.btn-header-action { 
    padding: 9px; 
    font-size: 16px; 
}


/* 提示词列表区域 */
.prompt-list-container {
    flex-grow: 1; /* Takes available space */
    overflow-y: auto; /* Allows this part to scroll */
    padding: 8px 0; 
}
.prompt-list-container::-webkit-scrollbar { width: 6px; }
.prompt-list-container::-webkit-scrollbar-track { background: #f8f9fa; }
.prompt-list-container::-webkit-scrollbar-thumb { background-color: #ced4da; border-radius: 3px; }
.prompt-list-container::-webkit-scrollbar-thumb:hover { background-color: #adb5bd; }

.prompt-item {
    padding: 14px 20px;
    border-bottom: 1px solid #f1f3f5; 
    cursor: default; 
    transition: background-color 0.15s ease;
}
.prompt-item:last-child { border-bottom: none; }
.prompt-item:hover { background-color: #f8f9fa; }

.prompt-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}
.prompt-title {
    font-size: 16px;
    font-weight: 600;
    color: #212529; 
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 110px); 
}
.prompt-actions { display: flex; gap: 6px; }
.prompt-content-preview {
    font-size: 13px;
    color: #495057; 
    line-height: 1.6;
    max-height: 42px; 
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 10px;
}
.prompt-tags { display: flex; flex-wrap: wrap; gap: 6px; }
.tag {
    background-color: #e5e7eb;
    color: #374151;
    padding: 2px 8px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
}

/* 添加/编辑表单视图 */
.form-view { 
    /* padding: 20px; Padding will be on inner elements */
    background-color: #fff;
    /* overflow-y: auto; This was causing issues, handled by child */
}

.form-view-header {
    padding: 20px 20px 15px 20px; /* Add padding here */
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-bottom: 15px; Removed, padding handles spacing */
    border-bottom: 1px solid #e9ecef; /* Add separator */
    flex-shrink: 0;
}
.form-view-header h2 { font-size: 18px; font-weight: 600; color: #343a40; margin: 0; }
.form-view-header .btn-icon { color: #6c757d; }
.form-view-header .btn-icon:hover { color: #343a40; }

.form-content-scrollable {
    flex-grow: 1; /* Allows this part to take available space and scroll */
    overflow-y: auto; /* Enables vertical scrolling for form groups */
    padding: 20px; /* Padding for the form groups area */
}
.form-content-scrollable::-webkit-scrollbar { width: 6px; }
.form-content-scrollable::-webkit-scrollbar-track { background: #f8f9fa; }
.form-content-scrollable::-webkit-scrollbar-thumb { background-color: #ced4da; border-radius: 3px; }
.form-content-scrollable::-webkit-scrollbar-thumb:hover { background-color: #adb5bd; }


.form-group {
    margin-bottom: 18px;
}
.form-group:last-child {
    margin-bottom: 0; /* Remove margin from last form group if inside scrollable */
}
.form-group label { 
    display: block; font-size: 13px; font-weight: 500; margin-bottom: 8px; color: #495057;
}
.form-group input[type="text"],
.form-group textarea {
    width: calc(100% - 28px); 
    padding: 12px 14px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}
.form-group input[type="text"]::placeholder,
.form-group textarea::placeholder { color: #adb5bd; }
.form-group input[type="text"]:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 111, 16, 0.15);
}
.form-group textarea { min-height: 100px; resize: vertical; }

.form-actions { 
    display: flex; 
    justify-content: flex-end; 
    gap: 12px; 
    padding: 15px 20px; /* Add padding here */
    flex-shrink: 0;
    border-top: 1px solid #e9ecef; 
}

/* 空状态提示 */
.empty-state { 
    text-align: center; 
    padding: 40px 20px; 
    color: #6c757d;
    display: flex; 
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%; 
}
.empty-state .icon { font-size: 48px; margin-bottom: 16px; color: #adb5bd; }
.empty-state p { font-size: 16px; margin-bottom: 20px; }

/* 图标辅助 */
.icon {
    width: 16px;
    height: 16px;
}

/* In dark mode make SVG icons light */
html.theme-dark img.icon {
    filter: invert(1) brightness(1.2);
}

/* Toast Notification */
.toast-notification {
    position: fixed;
    bottom: -50px; 
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s ease, bottom 0.3s ease;
    visibility: hidden;
}
.toast-notification.show {
    opacity: 1;
    bottom: 20px; 
    visibility: visible;
}