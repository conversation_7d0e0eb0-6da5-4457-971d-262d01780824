document.addEventListener('DOMContentLoaded', async () => {
    // Import storage module
    const { promptStorage } = await import('../shared/storage.js');
    
    // Load prompts from storage
    let currentPrompts = await promptStorage.loadPrompts();
    
    // Mock data for prompts (fallback)
    const mockPrompts = [
        {
            id: "1",
            title: "生成博客文章大纲",
            summary: "请为我关于“[主题]”的博客文章生成一个详细的大纲...",
            promptContent: "请为我关于“[主题]”的博客文章生成一个详细的大纲，包括引言、主要论点、支持细节和结论。确保大纲逻辑清晰，内容全面。",
            tags: ["写作", "博客", "大纲"],
            notes: "这是一个用于生成博客大纲的优质提示词。"
        },
        {
            id: "2",
            title: "解释Python代码段",
            summary: "下面是一段Python代码，请解释它的功能...",
            promptContent: "下面是一段Python代码，请解释它的功能，并指出任何潜在的改进点：[在此处粘贴代码]",
            tags: ["编程", "Python"],
            notes: ""
        },
        {
            id: "3",
            title: "头脑风暴会议议程",
            summary: "为下周关于新产品功能的头脑风暴会议制定一个议程...",
            promptContent: "为下周关于新产品功能的头脑风暴会议制定一个议程，确保包含目标、参与人员、时间分配和预期成果。",
            tags: ["会议", "效率"],
            notes: "适用于快速启动会议准备。"
        }
    ];

    const promptListContainer = document.getElementById('promptList');
    const emptyStateView = document.getElementById('emptyStateView');
    const searchInput = document.getElementById('searchInput');

    // View switching elements
    const mainView = document.getElementById('promptPalMainView');
    const addEditView = document.getElementById('addEditPromptView');
    const showAddFormBtn = document.getElementById('showAddFormBtn');
    const addFirstPromptBtn = document.getElementById('addFirstPromptBtn');
    const backToListBtn = document.getElementById('backToListBtn');
    const cancelFormBtnPopup = document.getElementById('cancelFormBtnPopup');
    const saveFormBtnPopup = document.getElementById('saveFormBtnPopup');
    const formTitlePopup = document.getElementById('formTitlePopup');
    
    // Form inputs
    const promptTitleInput = document.getElementById('promptTitleInputPopup');
    const promptContentInput = document.getElementById('promptContentInputPopup');
    const promptTagsInput = document.getElementById('promptTagsInputPopup');
    const promptNotesInput = document.getElementById('promptNotesInputPopup');

    // Toast notification
    const toastNotification = document.getElementById('toastNotification');
    let toastTimeout;

    function showToast(message) {
        if (toastTimeout) clearTimeout(toastTimeout);
        toastNotification.textContent = message;
        toastNotification.classList.add('show');
        toastTimeout = setTimeout(() => {
            toastNotification.classList.remove('show');
        }, 2500); // Show for 2.5 seconds
    }

    // Function to render prompts
    function renderPrompts(promptsToRender = currentPrompts) {
        // Before clearing, check if emptyStateView is a child. If so, remove it temporarily.
        let emptyStateWasChild = false;
        if (emptyStateView && promptListContainer.contains(emptyStateView)) {
            promptListContainer.removeChild(emptyStateView);
            emptyStateWasChild = true;
        }
        promptListContainer.innerHTML = ''; // Clear existing items
        
        if (promptsToRender.length === 0) {
            if(emptyStateView) {
                emptyStateView.style.display = 'flex'; // Show empty state
                promptListContainer.appendChild(emptyStateView); // Add it back
            }
        } else {
            if(emptyStateView) emptyStateView.style.display = 'none'; // Hide if it was re-added
             promptsToRender.forEach(prompt => {
                const promptItemDiv = document.createElement('div');
                promptItemDiv.className = 'prompt-item hover:bg-gray-50';
                promptItemDiv.dataset.id = prompt.id; // Store id for future operations

                let tagsHTML = '';
                if (prompt.tags && prompt.tags.length > 0) {
                    tagsHTML = prompt.tags.map(tag => `<span class="tag bg-gray-200 text-gray-700 px-2 py-1 rounded-full text-xs font-medium">${escapeHTML(tag)}</span>`).join('');
                }

                // Generate preview text
                const previewSource = prompt.summary && prompt.summary.trim() !== '' ?
                                        prompt.summary :
                                        (prompt.promptContent || prompt.summary || '');
                const previewText = previewSource.substring(0, 100) + (previewSource.length > 100 ? "..." : "");

                promptItemDiv.innerHTML = `
                    <div class="prompt-item-header">
                        <span class="prompt-title">${escapeHTML(prompt.title)}</span>
                        <div class="prompt-actions">
                            <button class="btn btn-icon copy-btn" title="复制">
                                <img src="../icons/copy.svg" class="icon" alt="copy">
                            </button>
                            <button class="btn btn-icon edit-btn" title="编辑">
                                <img src="../icons/edit.svg" class="icon" alt="edit">
                            </button>
                            <button class="btn btn-icon delete-btn" title="删除">
                                <img src="../icons/trash.svg" class="icon" alt="delete">
                            </button>
                        </div>
                    </div>
                    <p class="prompt-content-preview">${escapeHTML(previewText)}</p>
                    <div class="prompt-tags">${tagsHTML}</div>
                `;
                promptListContainer.appendChild(promptItemDiv);
            });
        }
    }
    
    // HTML escaping helper
    function escapeHTML(str) {
        if (typeof str !== 'string') return '';
        return str.replace(/[&<>"']/g, function (match) {
            return {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#39;'
            }[match];
        });
    }


    // Initial render
    renderPrompts();

    // --- View Switching Logic ---
    function switchToView(viewToShow) {
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active-view');
        });
        viewToShow.classList.add('active-view');
    }

    showAddFormBtn.addEventListener('click', () => {
        formTitlePopup.textContent = '添加新提示词';
        // Clear form fields for new entry
        promptTitleInput.value = '';
        promptContentInput.value = '';
        promptTagsInput.value = '';
        promptNotesInput.value = '';
        promptTitleInput.dataset.editingId = ''; // Clear editing ID
        switchToView(addEditView);
        promptTitleInput.focus();
    });
    
    if(addFirstPromptBtn) { // If empty state button exists
        addFirstPromptBtn.addEventListener('click', () => {
             showAddFormBtn.click(); // Simulate click on main add button
        });
    }

    backToListBtn.addEventListener('click', () => {
        switchToView(mainView);
    });
    cancelFormBtnPopup.addEventListener('click', () => {
         switchToView(mainView);
    });

    // --- Storage CRUD Operations ---
    saveFormBtnPopup.addEventListener('click', async () => { 
        const title = promptTitleInput.value.trim();
        const content = promptContentInput.value.trim();
        const tags = promptTagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);
        const notes = promptNotesInput.value.trim();
        const editingId = promptTitleInput.dataset.editingId;

        if (!title || !content) {
            showToast("标题和提示词内容不能为空！");
            return;
        }

        if (editingId) { // Editing existing prompt
            try {
                currentPrompts = await promptStorage.updatePrompt(editingId, {
                    title,
                    promptContent: content,
                    tags,
                    notes
                });
                showToast("提示词已更新！");
            } catch (error) {
                showToast("更新失败：" + error.message);
                return;
            }
        } else { // Adding new prompt
            try {
                currentPrompts = await promptStorage.addPrompt({
                    title,
                    promptContent: content,
                    tags,
                    notes
                });
                showToast("提示词已添加！");
            } catch (error) {
                showToast("添加失败：" + error.message);
                return;
            }
        }
        
        renderPrompts();
        switchToView(mainView);
    });

    // Event delegation for prompt item actions
    promptListContainer.addEventListener('click', (event) => {
        const targetButton = event.target.closest('button.btn-icon');
        if (!targetButton) return;

        const promptItemDiv = targetButton.closest('.prompt-item');
        const promptId = promptItemDiv.dataset.id;
        const promptData = currentPrompts.find(p => p.id === promptId);

        if (!promptData) return;

        if (targetButton.classList.contains('copy-btn')) {
            const contentToCopy = promptData.promptContent || '';
            navigator.clipboard.writeText(contentToCopy).then(() => {
                showToast('已复制到剪贴板！');
            }).catch(err => {
                showToast('复制失败!');
                console.error('Copy failed:', err);
            });
        } else if (targetButton.classList.contains('edit-btn')) {
            formTitlePopup.textContent = '编辑提示词';
            promptTitleInput.value = promptData.title;
            promptContentInput.value = promptData.promptContent || ''; // Edit full content
            promptTagsInput.value = promptData.tags.join(', ');
            promptNotesInput.value = promptData.notes;
            promptTitleInput.dataset.editingId = promptId; // Set ID for saving
            switchToView(addEditView);
            promptTitleInput.focus();
        } else if (targetButton.classList.contains('delete-btn')) {
            if (confirm(`确定要删除提示词 "${promptData.title}" 吗？`)) {
                try {
                    currentPrompts = await promptStorage.deletePrompt(promptId);
                    renderPrompts();
                    showToast('提示词已删除！');
                } catch (error) {
                    showToast('删除失败：' + error.message);
                }
            }
        }
    });
    
    // Search functionality
    searchInput.addEventListener('input', async (event) => {
        const searchTerm = event.target.value.toLowerCase();
        if (searchTerm === '') {
            renderPrompts(); // Show all prompts
        } else {
            try {
                const filteredPrompts = await promptStorage.searchPrompts(searchTerm);
                renderPrompts(filteredPrompts);
            } catch (error) {
                console.error('Search failed:', error);
                renderPrompts([]);
            }
        }
    });

    // Initial render of prompts from storage
    renderPrompts();

    // Management Page Button (Placeholder for now)
    const openManagementPageBtn = document.getElementById('openManagementPageBtn');
    if (openManagementPageBtn) {
        openManagementPageBtn.addEventListener('click', () => {
            // chrome.tabs.create({ url: chrome.runtime.getURL('management/management.html') });
            showToast("管理页面功能将在后续模块实现。");
            console.log("Attempting to open management page (placeholder).");
        });
    }

    const themeToggleBtn = document.getElementById('themeToggleBtn');
    function applyTheme() {
        const saved = localStorage.getItem('theme');
        const isDark = saved === 'dark';
        document.documentElement.classList.toggle('theme-dark', isDark);
        if (themeToggleBtn) {
            themeToggleBtn.querySelector('img').src = isDark ? '../icons/sun.svg' : '../icons/moon.svg';
        }
    }
    if (themeToggleBtn) {
        themeToggleBtn.addEventListener('click', () => {
            const nowDark = !document.documentElement.classList.contains('theme-dark');
            document.documentElement.classList.toggle('theme-dark', nowDark);
            localStorage.setItem('theme', nowDark ? 'dark' : 'light');
            themeToggleBtn.querySelector('img').src = nowDark ? '../icons/sun.svg' : '../icons/moon.svg';
        });
    }
    applyTheme();
});