<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI提示词助手</title>

    <!-- 内联CSS确保立即生效，防止闪白 -->
    <style>
      html, body {
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
        color: #333333;
        transition: background-color 0.2s ease, color 0.2s ease;
      }
      html.theme-dark, html.theme-dark body {
        background-color: #1e1e1e !important;
        color: #f0f0f0 !important;
      }
    </style>

    <script>
      (function() {
        try {
          const theme = localStorage.getItem('theme');
          if (theme === 'dark') {
            document.documentElement.classList.add('theme-dark');
          } else {
            document.documentElement.classList.remove('theme-dark');
          }
        } catch (e) {
          // 如果localStorage不可用，使用默认浅色主题
        }
      })();
    </script>

    <link rel="stylesheet" href="popup.css">
    </head>
<body>
    <div class="prompt-pal-popup" id="promptPalContainer">
        <div id="promptPalMainView" class="view active-view">
            <header class="popup-header">
                <input type="search" class="search-input" id="searchInput" placeholder="搜索提示词...">
                <button class="btn btn-primary btn-icon btn-header-action" id="showAddFormBtn" title="添加新提示词">
                    <img src="../icons/plus.svg" class="icon" alt="add">
                </button>
                <button class="btn btn-icon btn-header-action" id="openManagementPageBtn" title="打开管理页面">
                    <img src="../icons/settings.svg" class="icon" alt="settings">
                </button>
                <button class="btn btn-icon btn-header-action" id="themeToggleBtn" title="切换主题">
                    <img src="../icons/moon.svg" class="icon" alt="theme">
                </button>
            </header>

            <div class="prompt-list-container" id="promptList">
                <div class="empty-state" id="emptyStateView" style="display: none;">
                    <div class="icon">✨</div>
                    <p>还没有提示词呢！</p>
                    <button class="btn btn-primary" id="addFirstPromptBtn">添加第一个提示词</button>
                </div>
            </div>
        </div>

        <div class="form-view view" id="addEditPromptView">
            <div class="form-view-header">
                <h2 id="formTitlePopup">添加新提示词</h2>
                <button class="btn btn-icon" id="backToListBtn" title="返回列表">
                    <img src="../icons/arrow-left.svg" class="icon" alt="back">
                </button>
            </div>
            <div class="form-content-scrollable">
                <div class="form-group">
                    <label for="promptTitleInputPopup">标题</label>
                    <input type="text" id="promptTitleInputPopup" placeholder="例如：生成周报摘要">
                </div>
                <div class="form-group">
                    <label for="promptContentInputPopup">提示词内容</label>
                    <textarea id="promptContentInputPopup" rows="6" placeholder="输入你的提示词..."></textarea>
                </div>
                <div class="form-group">
                    <label for="promptTagsInputPopup">标签 (逗号分隔)</label>
                    <input type="text" id="promptTagsInputPopup" placeholder="例如：工作, 报告, 总结">
                </div>
                <div class="form-group">
                    <label for="promptNotesInputPopup">备注 (可选)</label>
                    <textarea id="promptNotesInputPopup" rows="3" placeholder="关于此提示词的额外说明..."></textarea>
                </div>
            </div>
            <div class="form-actions">
                <button class="btn btn-secondary" id="cancelFormBtnPopup">取消</button>
                <button class="btn btn-primary" id="saveFormBtnPopup">保存</button>
            </div>
        </div>
    </div>
    <div id="toastNotification" class="toast-notification">操作成功！</div>
    <script type="module" src="popup.js"></script>
</body>
</html>