<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI提示词助手</title>
    <link rel="stylesheet" href="popup.css">

    <script>
      (function() {
        try {
          const theme = localStorage.getItem('theme');
          if (theme === 'dark') {
            document.documentElement.classList.add('theme-dark');
          }
        } catch (e) {}
      })();
    </script>
    </head>
<body class="font-inter text-gray-800">
    <div class="prompt-pal-popup" id="promptPalContainer">
        <div id="promptPalMainView" class="view active-view">
            <header class="popup-header">
                <input type="search" class="search-input w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" id="searchInput" placeholder="搜索提示词...">
                <button class="btn btn-primary btn-icon btn-header-action bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700" id="showAddFormBtn" title="添加新提示词">
                    <img src="../icons/plus.svg" class="icon" alt="add">
                </button>
                <button class="btn btn-icon btn-header-action p-2 rounded-full hover:bg-gray-100" id="openManagementPageBtn" title="打开管理页面">
                    <img src="../icons/settings.svg" class="icon" alt="settings">
                </button>
                <button class="btn btn-icon btn-header-action p-2 rounded-full hover:bg-gray-100" id="themeToggleBtn" title="切换主题">
                    <img src="../icons/moon.svg" class="icon" alt="theme">
                </button>
            </header>

            <div class="prompt-list-container" id="promptList">
                <div class="empty-state" id="emptyStateView" style="display: none;">
                    <div class="icon">✨</div>
                    <p>还没有提示词呢！</p>
                    <button class="btn btn-primary bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" id="addFirstPromptBtn">添加第一个提示词</button>
                </div>
            </div>
        </div>

        <div class="form-view view" id="addEditPromptView">
            <div class="form-view-header">
                <h2 id="formTitlePopup" class="text-lg font-semibold">添加新提示词</h2>
                <button class="btn btn-icon" id="backToListBtn" title="返回列表">
                    <img src="../icons/arrow-left.svg" class="icon" alt="back">
                </button>
            </div>
            <div class="form-content-scrollable">
                <div class="form-group">
                    <label for="promptTitleInputPopup">标题</label>
                    <input type="text" id="promptTitleInputPopup" placeholder="例如：生成周报摘要">
                </div>
                <div class="form-group">
                    <label for="promptContentInputPopup">提示词内容</label>
                    <textarea id="promptContentInputPopup" rows="6" placeholder="输入你的提示词..."></textarea>
                </div>
                <div class="form-group">
                    <label for="promptTagsInputPopup">标签 (逗号分隔)</label>
                    <input type="text" id="promptTagsInputPopup" placeholder="例如：工作, 报告, 总结">
                </div>
                <div class="form-group">
                    <label for="promptNotesInputPopup">备注 (可选)</label>
                    <textarea id="promptNotesInputPopup" rows="3" placeholder="关于此提示词的额外说明..."></textarea>
                </div>
            </div>
            <div class="form-actions flex justify-end gap-3">
                <button class="btn btn-secondary bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600" id="cancelFormBtnPopup">取消</button>
                <button class="btn btn-primary bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700" id="saveFormBtnPopup">保存</button>
            </div>
        </div>
    </div>
    <div id="toastNotification" class="toast-notification">操作成功！</div>
    <script type="module" src="popup.js"></script>
</body>
</html>