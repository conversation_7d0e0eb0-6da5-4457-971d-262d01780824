
# AI提示词助手 (PromptMate)

**PromptMate** 是一款功能强大、交互完善的浏览器插件，旨在帮助用户高效地收集、管理和使用AI提示词（Prompts）。无论您是内容创作者、开发者还是AI爱好者，这款工具都能让您的提示词管理工作流变得前所未有的简单和高效。

## ✨ 主要功能

* **多样的交互界面**：
    * **侧边栏 (Side Panel)**：作为主要操作界面，可通过浏览器工具栏图标或网页悬浮球快速唤出。
    * **独立管理页面**：提供一个功能全面的Web页面，用于集中管理、搜索、排序和批量操作所有提示词。
    * **悬浮操作按钮 (FAB)**：在任意页面右下角显示一个可拖动的悬浮球，方便随时访问。
* **便捷的提示词收藏**：
    * **右键菜单收藏**：在网页上选中任意文本，通过右键菜单即可快速收藏，并能完整保留HTML格式。
* **强大的管理能力**：
    * **全功能CRUD**：支持提示词的增、删、改、查，表单包含标题、简介、正文、作者、版本和备注等丰富字段。
    * **交互式标签系统**：在添加或编辑时，标签输入框会根据已有的标签库提供建议和自动补全，管理更高效。
    * **高级搜索与排序**：支持按关键词在标题、内容、标签中进行全文搜索，并可按日期或标题进行排序。
    * **批量操作**：在管理页面中，可以方便地多选或全选提示词，进行批量删除。
* **灵活的数据迁移**：
    * **数据导入/导出**：支持将所有提示词数据导出为 `JSON` 或 `CSV` 格式，也支持从这两种格式的文件导入，方便备份和迁移。
    * **智能合并策略**：导入数据时，会通过ID和版本号进行智能去重。对于ID相同的数据，只有当导入的版本号高于现有版本时才会更新。
* **优秀的用户体验**：
    * **明暗主题切换**：所有界面均支持明暗两种主题，并能自动记忆用户的偏好设置。
    * **即时操作反馈**：所有关键操作（如复制、保存、删除）都会有Toast弹窗提示，用户体验流畅。
    * **本地化存储**：所有数据均存储在用户本地的 `chrome.storage.local` 中，确保了数据的隐私和安全。

## 🚀 安装与加载

作为本地开发的插件，您可以通过以下步骤在Chrome或Edge等Chromium内核的浏览器中加载：

1.  下载本项目所有文件到本地文件夹。
2.  打开浏览器的 **扩展程序** 管理页面。
    * Chrome: `chrome://extensions`
    * Edge: `edge://extensions`
3.  在页面右上角，打开 **“开发者模式”** 开关。
4.  点击左上角的 **“加载已解压的扩展程序”** 按钮。
5.  在文件选择窗口中，选择本项目所在的文件夹。
6.  加载成功后，"AI提示词助手 PromptMate" 将会出现在您的扩展程序列表中。

## 📖 使用指南

### 1. 打开助手

* **方法一**：点击浏览器工具栏右上角的 **PromptMate 图标**，打开侧边栏。
* **方法二**：在任意网页上，点击右下角的 **悬浮球（FAB）**，打开侧边栏。

### 2. 收藏提示词

1.  在任何网页上，用鼠标选中您希望收藏的文本内容。
2.  在选中的文本上点击鼠标右键。
3.  在弹出的上下文菜单中，选择：
    * **“使用 PromptMate 收藏”**：将选中的内容添加到助手的数据库中，并在侧边栏中打开编辑表单。
    * **“添加到 PromptMate 并打开管理页面”**：收藏内容并直接跳转到功能更全面的独立管理页面。

### 3. 管理提示词

* **快速管理 (侧边栏)**：
    * 在侧边栏顶部的搜索框中可以快速**搜索**提示词。
    * 点击每个提示词项上的**复制、编辑、删除**图标进行快速操作。
    * 点击 **“+”** 按钮可以添加新的提示词。
* **高级管理 (独立页面)**：
    * 在侧边栏中点击**文件图标**按钮，或通过右键菜单，可以进入独立管理页面。
    * 在这里，您可以进行**排序、批量删除、导入/导出**等高级操作。

## 🛠️ 技术栈

* **核心语言**: `HTML5`, `CSS3`, `JavaScript (ES6)`
* **浏览器 API**:
    * `Manifest V3`
    * `Side Panel API`
    * `Context Menus API`
    * `Storage API` (chrome.storage)
    * `Scripting API` (chrome.scripting)
* **代码结构**:
    * **模块化开发**：通过 `import/export` 组织代码，逻辑清晰。
    * **共享模块 (`/shared`)**：将通用的存储逻辑 (`storage.js`) 和配置 (`config.js`) 进行抽离，提高了代码的可维护性和复用性。

## 📁 项目结构

```
PromptAssist/
├── icons/                # 插件图标
├── backup/               # 旧版本文件备份
├── content_scripts/
│   ├── content_script.js # 内容脚本，用于注入悬浮球
│   └── content_script.css# 悬浮球样式
├── management_page/      # 独立管理页面
│   ├── management.js
│   ├── management.css
│   └── management.html
├── popup/                # 弹出窗口（当前版本未使用，可作为备用）
│   ├── popup.js
│   ├── popup.css
│   └── popup.html
├── shared/               # 共享模块
│   ├── storage.js        # 核心存储逻辑
│   ├── config.js         # 全局配置
│   └── README.md
├── sidebar/              # 侧边栏
│   ├── sidebar.js
│   ├── sidebar.css
│   └── sidebar.html
├── background.js         # Service Worker，处理后台逻辑
└── manifest.json         # 插件清单文件
```